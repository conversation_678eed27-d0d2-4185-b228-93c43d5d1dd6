@echo off
echo ========================================
echo 旅游管理系统启动脚本
echo ========================================

echo 1. 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装JDK
    pause
    exit /b 1
)

echo.
echo 2. 停止现有的Java进程...
taskkill /F /IM java.exe 2>nul
timeout /t 2 >nul

echo.
echo 3. 清理临时文件...
if exist "temp" rmdir /s /q temp
if exist "work" rmdir /s /q work
if exist "logs" rmdir /s /q logs

echo.
echo 4. 创建必要目录...
mkdir temp 2>nul
mkdir work 2>nul
mkdir logs 2>nul

echo.
echo 5. 设置环境变量...
set CATALINA_HOME=%CD%
set CATALINA_BASE=%CD%
set JAVA_OPTS=-Xms256m -Xmx512m -Dfile.encoding=UTF-8

echo.
echo 6. 启动Tomcat服务器...
echo 项目路径: %CD%
echo 访问地址: http://localhost:8080/webtest/
echo.
echo 正在启动服务器，请稍候...

java -cp "lib/*" ^
     -Djava.util.logging.config.file=conf/logging.properties ^
     -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager ^
     -Djava.endorsed.dirs=endorsed ^
     -Dcatalina.base=%CATALINA_BASE% ^
     -Dcatalina.home=%CATALINA_HOME% ^
     -Djava.io.tmpdir=temp ^
     -Dfile.encoding=UTF-8 ^
     org.apache.catalina.startup.Bootstrap start

echo.
echo 服务器已启动！
echo 请访问: http://localhost:8080/webtest/
echo 按任意键退出...
pause
