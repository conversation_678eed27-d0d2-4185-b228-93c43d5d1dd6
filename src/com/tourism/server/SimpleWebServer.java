package com.tourism.server;

import com.sun.net.httpserver.HttpServer;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpExchange;

import java.io.*;
import java.net.InetSocketAddress;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 简单的Web服务器
 * 用于快速启动和测试旅游管理系统
 */
public class SimpleWebServer {
    private static final int PORT = 8080;
    private static final String WEB_ROOT = "out/artifacts/webtest_war_exploded";
    
    public static void main(String[] args) throws IOException {
        HttpServer server = HttpServer.create(new InetSocketAddress(PORT), 0);
        
        // 静态文件处理器
        server.createContext("/webtest/", new StaticFileHandler());
        
        // 数据库测试处理器
        server.createContext("/webtest/database-test", new DatabaseTestHandler());
        
        server.setExecutor(null);
        server.start();
        
        System.out.println("========================================");
        System.out.println("🚀 旅游管理系统启动成功！");
        System.out.println("========================================");
        System.out.println("📍 服务器地址: http://localhost:" + PORT + "/webtest/");
        System.out.println("🏠 系统首页: http://localhost:" + PORT + "/webtest/index.jsp");
        System.out.println("🧪 功能测试: http://localhost:" + PORT + "/webtest/test-simple.jsp");
        System.out.println("🗄️ 数据库测试: http://localhost:" + PORT + "/webtest/database-test");
        System.out.println("========================================");
        System.out.println("💡 按 Ctrl+C 停止服务器");
        System.out.println("========================================");
    }
    
    static class StaticFileHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String requestPath = exchange.getRequestURI().getPath();
            
            // 移除 /webtest/ 前缀
            if (requestPath.startsWith("/webtest/")) {
                requestPath = requestPath.substring("/webtest/".length());
            }
            
            // 默认页面
            if (requestPath.isEmpty() || requestPath.equals("/")) {
                requestPath = "index.jsp";
            }
            
            Path filePath = Paths.get(WEB_ROOT, requestPath);
            
            if (Files.exists(filePath) && !Files.isDirectory(filePath)) {
                // 设置内容类型
                String contentType = getContentType(requestPath);
                exchange.getResponseHeaders().set("Content-Type", contentType + "; charset=UTF-8");
                
                // 读取文件内容
                byte[] fileContent = Files.readAllBytes(filePath);
                
                exchange.sendResponseHeaders(200, fileContent.length);
                try (OutputStream os = exchange.getResponseBody()) {
                    os.write(fileContent);
                }
            } else {
                // 404 Not Found
                String response = "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>404 - 页面未找到</title></head>" +
                                "<body><h1>404 - 页面未找到</h1><p>请求的页面不存在: " + requestPath + "</p>" +
                                "<p><a href='/webtest/'>返回首页</a></p></body></html>";
                exchange.getResponseHeaders().set("Content-Type", "text/html; charset=UTF-8");
                exchange.sendResponseHeaders(404, response.getBytes("UTF-8").length);
                try (OutputStream os = exchange.getResponseBody()) {
                    os.write(response.getBytes("UTF-8"));
                }
            }
        }
        
        private String getContentType(String fileName) {
            if (fileName.endsWith(".html") || fileName.endsWith(".htm")) {
                return "text/html";
            } else if (fileName.endsWith(".css")) {
                return "text/css";
            } else if (fileName.endsWith(".js")) {
                return "application/javascript";
            } else if (fileName.endsWith(".jsp")) {
                return "text/html";
            } else if (fileName.endsWith(".png")) {
                return "image/png";
            } else if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
                return "image/jpeg";
            } else if (fileName.endsWith(".gif")) {
                return "image/gif";
            } else {
                return "text/plain";
            }
        }
    }
    
    static class DatabaseTestHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            StringBuilder response = new StringBuilder();
            response.append("<!DOCTYPE html>");
            response.append("<html lang='zh-CN'>");
            response.append("<head>");
            response.append("<meta charset='UTF-8'>");
            response.append("<title>数据库连接测试</title>");
            response.append("<style>");
            response.append("body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }");
            response.append(".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }");
            response.append(".success { color: #28a745; }");
            response.append(".error { color: #dc3545; }");
            response.append(".warning { color: #ffc107; }");
            response.append(".info { color: #17a2b8; }");
            response.append("pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }");
            response.append(".btn { display: inline-block; padding: 8px 16px; margin: 5px; text-decoration: none; border-radius: 4px; color: white; }");
            response.append(".btn-primary { background: #007bff; }");
            response.append(".btn-success { background: #28a745; }");
            response.append("</style>");
            response.append("</head>");
            response.append("<body>");
            response.append("<div class='container'>");
            response.append("<h1>🗄️ 数据库连接测试</h1>");
            
            // 测试数据库连接
            try {
                Class.forName("com.tourism.util.DatabaseUtil");
                response.append("<h2>连接测试</h2>");
                response.append("<p class='success'>✅ 数据库工具类加载成功</p>");
                
                // 这里可以添加实际的数据库连接测试
                response.append("<p class='warning'>⚠️ 需要配置MySQL数据库才能完全测试连接</p>");
                
            } catch (ClassNotFoundException e) {
                response.append("<p class='error'>❌ 数据库工具类加载失败: " + e.getMessage() + "</p>");
            }
            
            response.append("<h2>📋 系统状态</h2>");
            response.append("<ul>");
            response.append("<li class='success'>✅ Web服务器运行正常</li>");
            response.append("<li class='success'>✅ 静态文件服务正常</li>");
            response.append("<li class='warning'>⚠️ 需要启动MySQL服务</li>");
            response.append("<li class='warning'>⚠️ 需要创建tourism_system数据库</li>");
            response.append("</ul>");
            
            response.append("<h2>🚀 快速操作</h2>");
            response.append("<a href='/webtest/' class='btn btn-primary'>🏠 返回首页</a>");
            response.append("<a href='/webtest/test-simple.jsp' class='btn btn-success'>🧪 功能测试</a>");
            response.append("<a href='/webtest/database-test' class='btn btn-primary'>🔄 刷新测试</a>");
            
            response.append("<h2>💡 使用说明</h2>");
            response.append("<ol>");
            response.append("<li>当前使用简化的Web服务器，仅支持静态文件访问</li>");
            response.append("<li>要使用完整功能，需要配置Tomcat和MySQL</li>");
            response.append("<li>可以查看JSP页面的源代码了解系统结构</li>");
            response.append("</ol>");
            
            response.append("</div>");
            response.append("</body>");
            response.append("</html>");
            
            exchange.getResponseHeaders().set("Content-Type", "text/html; charset=UTF-8");
            byte[] responseBytes = response.toString().getBytes("UTF-8");
            exchange.sendResponseHeaders(200, responseBytes.length);
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(responseBytes);
            }
        }
    }
}
