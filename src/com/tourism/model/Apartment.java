package com.tourism.model;

/**
 * 公寓实体类
 */
public class Apartment {
    private int id;
    private String name;
    private String address;
    private String phone;
    private String email;
    private String roomType;
    private int roomCount;
    private double pricePerNight;
    private String facilities;
    private String manager;
    private String status;
    private String description;
    
    // 默认构造函数
    public Apartment() {
        this.status = "可预订";
    }
    
    // 带参数的构造函数
    public Apartment(String name, String address, String phone, String email, 
                    String roomType, int roomCount, double pricePerNight) {
        this.name = name;
        this.address = address;
        this.phone = phone;
        this.email = email;
        this.roomType = roomType;
        this.roomCount = roomCount;
        this.pricePerNight = pricePerNight;
        this.status = "可预订";
    }
    
    // Getter 和 Setter 方法
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getRoomType() {
        return roomType;
    }
    
    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }
    
    public int getRoomCount() {
        return roomCount;
    }
    
    public void setRoomCount(int roomCount) {
        this.roomCount = roomCount;
    }
    
    public double getPricePerNight() {
        return pricePerNight;
    }
    
    public void setPricePerNight(double pricePerNight) {
        this.pricePerNight = pricePerNight;
    }
    
    public String getFacilities() {
        return facilities;
    }
    
    public void setFacilities(String facilities) {
        this.facilities = facilities;
    }
    
    public String getManager() {
        return manager;
    }
    
    public void setManager(String manager) {
        this.manager = manager;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Override
    public String toString() {
        return "Apartment{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", address='" + address + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", roomType='" + roomType + '\'' +
                ", roomCount=" + roomCount +
                ", pricePerNight=" + pricePerNight +
                ", facilities='" + facilities + '\'' +
                ", manager='" + manager + '\'' +
                ", status='" + status + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
