package com.tourism.model;

/**
 * 星级饭店实体类
 */
public class Hotel {
    private int id;
    private String name;
    private String address;
    private String phone;
    private String email;
    private int starLevel; // 星级：1-5星
    private int roomCount;
    private String facilities; // 设施描述
    private String manager;
    private String status; // 营业、停业、装修
    private String description;
    
    public Hotel() {}
    
    public Hotel(String name, String address, String phone, String email, int starLevel, int roomCount) {
        this.name = name;
        this.address = address;
        this.phone = phone;
        this.email = email;
        this.starLevel = starLevel;
        this.roomCount = roomCount;
        this.status = "营业";
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public int getStarLevel() {
        return starLevel;
    }
    
    public void setStarLevel(int starLevel) {
        this.starLevel = starLevel;
    }
    
    public int getRoomCount() {
        return roomCount;
    }
    
    public void setRoomCount(int roomCount) {
        this.roomCount = roomCount;
    }
    
    public String getFacilities() {
        return facilities;
    }
    
    public void setFacilities(String facilities) {
        this.facilities = facilities;
    }
    
    public String getManager() {
        return manager;
    }
    
    public void setManager(String manager) {
        this.manager = manager;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Override
    public String toString() {
        return "Hotel{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", address='" + address + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", starLevel=" + starLevel +
                ", roomCount=" + roomCount +
                ", facilities='" + facilities + '\'' +
                ", manager='" + manager + '\'' +
                ", status='" + status + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
