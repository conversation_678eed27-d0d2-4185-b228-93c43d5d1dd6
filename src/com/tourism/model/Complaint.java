package com.tourism.model;

import java.sql.Timestamp;

/**
 * 投诉实体类
 */
public class Complaint {
    private int id;
    private String customerName;
    private String customerPhone;
    private String customerEmail;
    private String complaintType;
    private String complaintContent;
    private String relatedService;
    private Timestamp complaintDate;
    private String status;
    private String priority;
    private String handler;
    private Timestamp handleDate;
    private String handleResult;
    private String description;
    
    // 默认构造函数
    public Complaint() {
        this.status = "待处理";
        this.priority = "普通";
        this.complaintDate = new Timestamp(System.currentTimeMillis());
    }
    
    // 带参数的构造函数
    public Complaint(String customerName, String customerPhone, String customerEmail,
                    String complaintType, String complaintContent) {
        this.customerName = customerName;
        this.customerPhone = customerPhone;
        this.customerEmail = customerEmail;
        this.complaintType = complaintType;
        this.complaintContent = complaintContent;
        this.status = "待处理";
        this.priority = "普通";
        this.complaintDate = new Timestamp(System.currentTimeMillis());
    }
    
    // Getter 和 Setter 方法
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getCustomerName() {
        return customerName;
    }
    
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
    
    public String getCustomerPhone() {
        return customerPhone;
    }
    
    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }
    
    public String getCustomerEmail() {
        return customerEmail;
    }
    
    public void setCustomerEmail(String customerEmail) {
        this.customerEmail = customerEmail;
    }
    
    public String getComplaintType() {
        return complaintType;
    }
    
    public void setComplaintType(String complaintType) {
        this.complaintType = complaintType;
    }
    
    public String getComplaintContent() {
        return complaintContent;
    }
    
    public void setComplaintContent(String complaintContent) {
        this.complaintContent = complaintContent;
    }
    
    public String getRelatedService() {
        return relatedService;
    }
    
    public void setRelatedService(String relatedService) {
        this.relatedService = relatedService;
    }
    
    public Timestamp getComplaintDate() {
        return complaintDate;
    }
    
    public void setComplaintDate(Timestamp complaintDate) {
        this.complaintDate = complaintDate;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getPriority() {
        return priority;
    }
    
    public void setPriority(String priority) {
        this.priority = priority;
    }
    
    public String getHandler() {
        return handler;
    }
    
    public void setHandler(String handler) {
        this.handler = handler;
    }
    
    public Timestamp getHandleDate() {
        return handleDate;
    }
    
    public void setHandleDate(Timestamp handleDate) {
        this.handleDate = handleDate;
    }
    
    public String getHandleResult() {
        return handleResult;
    }
    
    public void setHandleResult(String handleResult) {
        this.handleResult = handleResult;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Override
    public String toString() {
        return "Complaint{" +
                "id=" + id +
                ", customerName='" + customerName + '\'' +
                ", customerPhone='" + customerPhone + '\'' +
                ", customerEmail='" + customerEmail + '\'' +
                ", complaintType='" + complaintType + '\'' +
                ", complaintContent='" + complaintContent + '\'' +
                ", relatedService='" + relatedService + '\'' +
                ", complaintDate=" + complaintDate +
                ", status='" + status + '\'' +
                ", priority='" + priority + '\'' +
                ", handler='" + handler + '\'' +
                ", handleDate=" + handleDate +
                ", handleResult='" + handleResult + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
