package com.tourism.model;

import java.util.Date;

/**
 * 游客实体类
 */
public class Tourist {
    private int id;
    private String name;
    private String idCard;
    private String phone;
    private String email;
    private String address;
    private Date registerDate;
    private String status; // 活跃、非活跃
    private String remarks;
    private String customerType; // 客户类型：VIP客户、常客、新客户、商务客户、家庭客户
    private String preferences; // 旅游偏好
    private String notes; // 备注
    
    public Tourist() {}
    
    public Tourist(String name, String idCard, String phone, String email, String address) {
        this.name = name;
        this.idCard = idCard;
        this.phone = phone;
        this.email = email;
        this.address = address;
        this.registerDate = new Date();
        this.status = "活跃";
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getIdCard() {
        return idCard;
    }
    
    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public Date getRegisterDate() {
        return registerDate;
    }
    
    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getRemarks() {
        return remarks;
    }
    
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getPreferences() {
        return preferences;
    }

    public void setPreferences(String preferences) {
        this.preferences = preferences;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    @Override
    public String toString() {
        return "Tourist{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", idCard='" + idCard + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", address='" + address + '\'' +
                ", registerDate=" + registerDate +
                ", status='" + status + '\'' +
                ", remarks='" + remarks + '\'' +
                ", customerType='" + customerType + '\'' +
                ", preferences='" + preferences + '\'' +
                ", notes='" + notes + '\'' +
                '}';
    }
}
