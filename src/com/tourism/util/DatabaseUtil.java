package com.tourism.util;

import java.io.IOException;
import java.io.InputStream;
import java.sql.*;
import java.util.Properties;

/**
 * 数据库工具类
 * 提供数据库连接和基本操作
 */
public class DatabaseUtil {
    private static String DRIVER;
    private static String URL;
    private static String USERNAME;
    private static String PASSWORD;

    static {
        loadDatabaseConfig();
        try {
            Class.forName(DRIVER);
            System.out.println("数据库驱动加载成功: " + DRIVER);
        } catch (ClassNotFoundException e) {
            System.err.println("数据库驱动加载失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 加载数据库配置
     */
    private static void loadDatabaseConfig() {
        Properties props = new Properties();
        InputStream input = null;

        try {
            // 尝试从类路径加载配置文件
            input = DatabaseUtil.class.getClassLoader().getResourceAsStream("database.properties");

            if (input != null) {
                props.load(input);
                DRIVER = props.getProperty("db.driver", "com.mysql.cj.jdbc.Driver");
                URL = props.getProperty("db.url", "******************************************************************************************************************************");
                USERNAME = props.getProperty("db.username", "root");
                PASSWORD = props.getProperty("db.password", "root");
                System.out.println("✅ 数据库配置文件加载成功");
                System.out.println("📍 数据库URL: " + URL);
                System.out.println("👤 数据库用户: " + USERNAME);
                System.out.println("🔑 密码状态: " + (PASSWORD != null && !PASSWORD.isEmpty() ? "已设置" : "未设置"));
            } else {
                // 使用默认配置
                System.out.println("⚠️ 未找到database.properties文件，使用默认配置");
                DRIVER = "com.mysql.cj.jdbc.Driver";
                URL = "******************************************************************************************************************************";
                USERNAME = "root";
                PASSWORD = "root";
                System.out.println("📍 默认URL: " + URL);
                System.out.println("👤 默认用户: " + USERNAME);
            }
        } catch (IOException e) {
            System.err.println("❌ 加载数据库配置失败，使用默认配置: " + e.getMessage());
            // 使用默认配置
            DRIVER = "com.mysql.cj.jdbc.Driver";
            URL = "******************************************************************************************************************************";
            USERNAME = "root";
            PASSWORD = "root";
        } finally {
            if (input != null) {
                try {
                    input.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    
    /**
     * 获取数据库连接
     */
    public static Connection getConnection() throws SQLException {
        try {
            Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            System.out.println("数据库连接成功");
            return conn;
        } catch (SQLException e) {
            System.err.println("数据库连接失败: " + e.getMessage());
            System.err.println("URL: " + URL);
            System.err.println("用户名: " + USERNAME);
            throw e;
        }
    }

    /**
     * 测试数据库连接
     */
    public static boolean testConnection() {
        Connection conn = null;
        try {
            conn = getConnection();
            System.out.println("数据库连接测试成功！");
            return true;
        } catch (SQLException e) {
            System.err.println("数据库连接测试失败: " + e.getMessage());
            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 获取数据库配置信息
     */
    public static String getDatabaseInfo() {
        return "数据库配置信息:\n" +
               "驱动: " + DRIVER + "\n" +
               "URL: " + URL + "\n" +
               "用户名: " + USERNAME + "\n" +
               "密码: " + (PASSWORD != null && !PASSWORD.isEmpty() ? "已设置" : "未设置");
    }
    
    /**
     * 关闭数据库连接
     */
    public static void closeConnection(Connection conn, PreparedStatement pstmt, ResultSet rs) {
        try {
            if (rs != null) rs.close();
            if (pstmt != null) pstmt.close();
            if (conn != null) conn.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 关闭数据库连接（无ResultSet）
     */
    public static void closeConnection(Connection conn, PreparedStatement pstmt) {
        closeConnection(conn, pstmt, null);
    }
    
    /**
     * 执行更新操作（INSERT, UPDATE, DELETE）
     */
    public static int executeUpdate(String sql, Object... params) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = getConnection();
            pstmt = conn.prepareStatement(sql);
            
            // 设置参数
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
            
            return pstmt.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
            return 0;
        } finally {
            closeConnection(conn, pstmt);
        }
    }
    
    /**
     * 执行查询操作
     */
    public static ResultSet executeQuery(String sql, Object... params) {
        try {
            Connection conn = getConnection();
            PreparedStatement pstmt = conn.prepareStatement(sql);
            
            // 设置参数
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
            
            return pstmt.executeQuery();
        } catch (SQLException e) {
            e.printStackTrace();
            return null;
        }
    }
}
