package com.tourism.servlet;

import com.tourism.dao.EnterpriseDAO;
import com.tourism.model.Enterprise;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Date;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 企业档案管理Servlet
 */
public class EnterpriseServlet extends HttpServlet {
    private EnterpriseDAO enterpriseDAO = new EnterpriseDAO();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String action = request.getParameter("action");
        
        if (action == null) {
            action = "list";
        }
        
        switch (action) {
            case "list":
                listEnterprises(request, response);
                break;
            case "add":
                showAddForm(request, response);
                break;
            case "edit":
                showEditForm(request, response);
                break;
            case "delete":
                deleteEnterprise(request, response);
                break;
            case "search":
                searchEnterprises(request, response);
                break;
            case "statistics":
                showStatistics(request, response);
                break;
            case "active":
                listActiveEnterprises(request, response);
                break;
            default:
                listEnterprises(request, response);
                break;
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        String action = request.getParameter("action");
        
        if ("add".equals(action)) {
            addEnterprise(request, response);
        } else if ("update".equals(action)) {
            updateEnterprise(request, response);
        }
    }
    
    /**
     * 显示企业档案列表
     */
    private void listEnterprises(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        List<Enterprise> enterprises = enterpriseDAO.getAllEnterprises();
        request.setAttribute("enterprises", enterprises);
        request.getRequestDispatcher("/enterprise-list.jsp").forward(request, response);
    }
    
    /**
     * 显示正常状态企业列表
     */
    private void listActiveEnterprises(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        List<Enterprise> enterprises = enterpriseDAO.getEnterprisesByStatus("正常");
        request.setAttribute("enterprises", enterprises);
        request.setAttribute("activeOnly", true);
        request.getRequestDispatcher("/enterprise-list.jsp").forward(request, response);
    }
    
    /**
     * 显示添加企业档案表单
     */
    private void showAddForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.getRequestDispatcher("/enterprise-add.jsp").forward(request, response);
    }
    
    /**
     * 添加企业档案
     */
    private void addEnterprise(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String name = request.getParameter("name");
        String type = request.getParameter("type");
        String licenseNumber = request.getParameter("licenseNumber");
        String legalPerson = request.getParameter("legalPerson");
        String address = request.getParameter("address");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String businessScope = request.getParameter("businessScope");
        String registrationDateStr = request.getParameter("registrationDate");
        String status = request.getParameter("status");
        String description = request.getParameter("description");
        
        // 验证必填字段
        if (name == null || name.trim().isEmpty() || 
            type == null || type.trim().isEmpty() || 
            licenseNumber == null || licenseNumber.trim().isEmpty() || 
            legalPerson == null || legalPerson.trim().isEmpty() || 
            address == null || address.trim().isEmpty() || 
            phone == null || phone.trim().isEmpty()) {
            request.getSession().setAttribute("error", "请填写所有必填字段！");
            response.sendRedirect(request.getContextPath() + "/enterprise?action=add");
            return;
        }
        
        // 检查营业执照号是否已存在
        if (enterpriseDAO.isLicenseNumberExists(licenseNumber, 0)) {
            request.getSession().setAttribute("error", "营业执照号已存在！");
            response.sendRedirect(request.getContextPath() + "/enterprise?action=add");
            return;
        }
        
        Enterprise enterprise = new Enterprise();
        enterprise.setName(name);
        enterprise.setType(type);
        enterprise.setLicenseNumber(licenseNumber);
        enterprise.setLegalPerson(legalPerson);
        enterprise.setAddress(address);
        enterprise.setPhone(phone);
        enterprise.setEmail(email);
        enterprise.setBusinessScope(businessScope);
        
        // 处理注册日期
        if (registrationDateStr != null && !registrationDateStr.trim().isEmpty()) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                java.util.Date utilDate = sdf.parse(registrationDateStr);
                enterprise.setRegistrationDate(new Date(utilDate.getTime()));
            } catch (ParseException e) {
                request.getSession().setAttribute("error", "注册日期格式不正确！");
                response.sendRedirect(request.getContextPath() + "/enterprise?action=add");
                return;
            }
        }
        
        enterprise.setStatus(status != null && !status.trim().isEmpty() ? status : "正常");
        enterprise.setDescription(description);
        
        boolean success = enterpriseDAO.addEnterprise(enterprise);
        
        if (success) {
            request.getSession().setAttribute("message", "企业档案添加成功！");
        } else {
            request.getSession().setAttribute("error", "企业档案添加失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/enterprise?action=list");
    }
    
    /**
     * 显示编辑企业档案表单
     */
    private void showEditForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        Enterprise enterprise = enterpriseDAO.getEnterpriseById(id);
        request.setAttribute("enterprise", enterprise);
        request.getRequestDispatcher("/enterprise-edit.jsp").forward(request, response);
    }
    
    /**
     * 更新企业档案信息
     */
    private void updateEnterprise(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        String name = request.getParameter("name");
        String type = request.getParameter("type");
        String licenseNumber = request.getParameter("licenseNumber");
        String legalPerson = request.getParameter("legalPerson");
        String address = request.getParameter("address");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String businessScope = request.getParameter("businessScope");
        String registrationDateStr = request.getParameter("registrationDate");
        String status = request.getParameter("status");
        String description = request.getParameter("description");
        
        // 验证必填字段
        if (name == null || name.trim().isEmpty() || 
            type == null || type.trim().isEmpty() || 
            licenseNumber == null || licenseNumber.trim().isEmpty() || 
            legalPerson == null || legalPerson.trim().isEmpty() || 
            address == null || address.trim().isEmpty() || 
            phone == null || phone.trim().isEmpty()) {
            request.getSession().setAttribute("error", "请填写所有必填字段！");
            response.sendRedirect(request.getContextPath() + "/enterprise?action=edit&id=" + id);
            return;
        }
        
        // 检查营业执照号是否已存在（排除当前记录）
        if (enterpriseDAO.isLicenseNumberExists(licenseNumber, id)) {
            request.getSession().setAttribute("error", "营业执照号已存在！");
            response.sendRedirect(request.getContextPath() + "/enterprise?action=edit&id=" + id);
            return;
        }
        
        Enterprise enterprise = enterpriseDAO.getEnterpriseById(id);
        if (enterprise != null) {
            enterprise.setName(name);
            enterprise.setType(type);
            enterprise.setLicenseNumber(licenseNumber);
            enterprise.setLegalPerson(legalPerson);
            enterprise.setAddress(address);
            enterprise.setPhone(phone);
            enterprise.setEmail(email);
            enterprise.setBusinessScope(businessScope);
            
            // 处理注册日期
            if (registrationDateStr != null && !registrationDateStr.trim().isEmpty()) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    java.util.Date utilDate = sdf.parse(registrationDateStr);
                    enterprise.setRegistrationDate(new Date(utilDate.getTime()));
                } catch (ParseException e) {
                    request.getSession().setAttribute("error", "注册日期格式不正确！");
                    response.sendRedirect(request.getContextPath() + "/enterprise?action=edit&id=" + id);
                    return;
                }
            }
            
            enterprise.setStatus(status);
            enterprise.setDescription(description);
            
            boolean success = enterpriseDAO.updateEnterprise(enterprise);
            
            if (success) {
                request.getSession().setAttribute("message", "企业档案更新成功！");
            } else {
                request.getSession().setAttribute("error", "企业档案更新失败！");
            }
        }
        
        response.sendRedirect(request.getContextPath() + "/enterprise?action=list");
    }
    
    /**
     * 删除企业档案
     */
    private void deleteEnterprise(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        boolean success = enterpriseDAO.deleteEnterprise(id);
        
        if (success) {
            request.getSession().setAttribute("message", "企业档案删除成功！");
        } else {
            request.getSession().setAttribute("error", "企业档案删除失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/enterprise?action=list");
    }
    
    /**
     * 搜索企业档案
     */
    private void searchEnterprises(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String keyword = request.getParameter("keyword");
        String type = request.getParameter("type");
        String status = request.getParameter("status");
        
        List<Enterprise> enterprises;
        
        if (status != null && !status.trim().isEmpty()) {
            // 按状态搜索
            enterprises = enterpriseDAO.getEnterprisesByStatus(status);
        } else if (type != null && !type.trim().isEmpty()) {
            // 按类型搜索
            enterprises = enterpriseDAO.getEnterprisesByType(type);
        } else if (keyword != null && !keyword.trim().isEmpty()) {
            // 按名称搜索
            enterprises = enterpriseDAO.searchEnterprisesByName(keyword);
        } else {
            // 显示所有
            enterprises = enterpriseDAO.getAllEnterprises();
        }
        
        request.setAttribute("enterprises", enterprises);
        request.setAttribute("keyword", keyword);
        request.setAttribute("type", type);
        request.setAttribute("status", status);
        request.getRequestDispatcher("/enterprise-list.jsp").forward(request, response);
    }
    
    /**
     * 显示统计信息
     */
    private void showStatistics(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int activeCount = enterpriseDAO.getActiveEnterpriseCount();
        int hotelCount = enterpriseDAO.getEnterpriseCountByType("酒店");
        int travelAgencyCount = enterpriseDAO.getEnterpriseCountByType("旅行社");
        int transportCount = enterpriseDAO.getEnterpriseCountByType("交通运输");
        int attractionCount = enterpriseDAO.getEnterpriseCountByType("景区景点");
        int otherCount = enterpriseDAO.getEnterpriseCountByType("其他");
        
        request.setAttribute("activeCount", activeCount);
        request.setAttribute("hotelCount", hotelCount);
        request.setAttribute("travelAgencyCount", travelAgencyCount);
        request.setAttribute("transportCount", transportCount);
        request.setAttribute("attractionCount", attractionCount);
        request.setAttribute("otherCount", otherCount);
        request.getRequestDispatcher("/enterprise-statistics.jsp").forward(request, response);
    }
}
