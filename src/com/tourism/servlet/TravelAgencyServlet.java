package com.tourism.servlet;

import com.tourism.dao.TravelAgencyDAO;
import com.tourism.model.TravelAgency;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 旅行社管理Servlet
 */
public class TravelAgencyServlet extends HttpServlet {
    private TravelAgencyDAO agencyDAO = new TravelAgencyDAO();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String action = request.getParameter("action");
        
        if (action == null) {
            action = "list";
        }
        
        switch (action) {
            case "list":
                listTravelAgencies(request, response);
                break;
            case "add":
                showAddForm(request, response);
                break;
            case "edit":
                showEditForm(request, response);
                break;
            case "delete":
                deleteTravelAgency(request, response);
                break;
            case "search":
                searchTravelAgencies(request, response);
                break;
            default:
                listTravelAgencies(request, response);
                break;
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        String action = request.getParameter("action");
        
        if ("add".equals(action)) {
            addTravelAgency(request, response);
        } else if ("update".equals(action)) {
            updateTravelAgency(request, response);
        }
    }
    
    /**
     * 显示旅行社列表
     */
    private void listTravelAgencies(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        List<TravelAgency> agencies = agencyDAO.getAllTravelAgencies();
        request.setAttribute("agencies", agencies);
        request.getRequestDispatcher("/agency-list.jsp").forward(request, response);
    }
    
    /**
     * 显示添加旅行社表单
     */
    private void showAddForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.getRequestDispatcher("/agency-add.jsp").forward(request, response);
    }
    
    /**
     * 添加旅行社
     */
    private void addTravelAgency(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String name = request.getParameter("name");
        String address = request.getParameter("address");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String licenseNumber = request.getParameter("licenseNumber");
        String manager = request.getParameter("manager");
        String businessScope = request.getParameter("businessScope");
        String description = request.getParameter("description");
        
        // 检查营业执照号是否已存在
        if (agencyDAO.isLicenseNumberExists(licenseNumber, 0)) {
            request.getSession().setAttribute("error", "营业执照号已存在，请检查后重新输入！");
            response.sendRedirect(request.getContextPath() + "/agency?action=add");
            return;
        }
        
        TravelAgency agency = new TravelAgency(name, address, phone, email, licenseNumber);
        agency.setManager(manager);
        agency.setBusinessScope(businessScope);
        agency.setDescription(description);
        
        boolean success = agencyDAO.addTravelAgency(agency);
        
        if (success) {
            request.getSession().setAttribute("message", "旅行社添加成功！");
        } else {
            request.getSession().setAttribute("error", "旅行社添加失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/agency?action=list");
    }
    
    /**
     * 显示编辑旅行社表单
     */
    private void showEditForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        TravelAgency agency = agencyDAO.getTravelAgencyById(id);
        request.setAttribute("agency", agency);
        request.getRequestDispatcher("/agency-edit.jsp").forward(request, response);
    }
    
    /**
     * 更新旅行社信息
     */
    private void updateTravelAgency(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        String name = request.getParameter("name");
        String address = request.getParameter("address");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String licenseNumber = request.getParameter("licenseNumber");
        String manager = request.getParameter("manager");
        String businessScope = request.getParameter("businessScope");
        String status = request.getParameter("status");
        String description = request.getParameter("description");
        
        // 检查营业执照号是否已存在（排除当前记录）
        if (agencyDAO.isLicenseNumberExists(licenseNumber, id)) {
            request.getSession().setAttribute("error", "营业执照号已存在，请检查后重新输入！");
            response.sendRedirect(request.getContextPath() + "/agency?action=edit&id=" + id);
            return;
        }
        
        TravelAgency agency = new TravelAgency();
        agency.setId(id);
        agency.setName(name);
        agency.setAddress(address);
        agency.setPhone(phone);
        agency.setEmail(email);
        agency.setLicenseNumber(licenseNumber);
        agency.setManager(manager);
        agency.setBusinessScope(businessScope);
        agency.setStatus(status);
        agency.setDescription(description);
        
        boolean success = agencyDAO.updateTravelAgency(agency);
        
        if (success) {
            request.getSession().setAttribute("message", "旅行社信息更新成功！");
        } else {
            request.getSession().setAttribute("error", "旅行社信息更新失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/agency?action=list");
    }
    
    /**
     * 删除旅行社
     */
    private void deleteTravelAgency(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        boolean success = agencyDAO.deleteTravelAgency(id);
        
        if (success) {
            request.getSession().setAttribute("message", "旅行社删除成功！");
        } else {
            request.getSession().setAttribute("error", "旅行社删除失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/agency?action=list");
    }
    
    /**
     * 搜索旅行社
     */
    private void searchTravelAgencies(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String keyword = request.getParameter("keyword");
        String status = request.getParameter("status");
        List<TravelAgency> agencies;
        
        if (status != null && !status.trim().isEmpty()) {
            // 按状态搜索
            agencies = agencyDAO.getTravelAgenciesByStatus(status);
        } else if (keyword != null && !keyword.trim().isEmpty()) {
            // 按名称搜索
            agencies = agencyDAO.searchTravelAgenciesByName(keyword);
        } else {
            // 显示所有
            agencies = agencyDAO.getAllTravelAgencies();
        }
        
        request.setAttribute("agencies", agencies);
        request.setAttribute("keyword", keyword);
        request.setAttribute("status", status);
        request.getRequestDispatcher("/agency-list.jsp").forward(request, response);
    }
}
