package com.tourism.servlet;

import com.tourism.dao.HotelDAO;
import com.tourism.model.Hotel;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 星级饭店管理Servlet
 */
public class HotelServlet extends HttpServlet {
    private HotelDAO hotelDAO = new HotelDAO();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String action = request.getParameter("action");
        
        if (action == null) {
            action = "list";
        }
        
        switch (action) {
            case "list":
                listHotels(request, response);
                break;
            case "add":
                showAddForm(request, response);
                break;
            case "edit":
                showEditForm(request, response);
                break;
            case "delete":
                deleteHotel(request, response);
                break;
            case "search":
                searchHotels(request, response);
                break;
            default:
                listHotels(request, response);
                break;
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        String action = request.getParameter("action");
        
        if ("add".equals(action)) {
            addHotel(request, response);
        } else if ("update".equals(action)) {
            updateHotel(request, response);
        }
    }
    
    /**
     * 显示饭店列表
     */
    private void listHotels(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        List<Hotel> hotels = hotelDAO.getAllHotels();
        request.setAttribute("hotels", hotels);
        request.getRequestDispatcher("/hotel-list.jsp").forward(request, response);
    }
    
    /**
     * 显示添加饭店表单
     */
    private void showAddForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.getRequestDispatcher("/hotel-add.jsp").forward(request, response);
    }
    
    /**
     * 添加饭店
     */
    private void addHotel(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String name = request.getParameter("name");
        String address = request.getParameter("address");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        int starLevel = Integer.parseInt(request.getParameter("starLevel"));
        int roomCount = Integer.parseInt(request.getParameter("roomCount"));
        String facilities = request.getParameter("facilities");
        String manager = request.getParameter("manager");
        String description = request.getParameter("description");
        
        Hotel hotel = new Hotel(name, address, phone, email, starLevel, roomCount);
        hotel.setFacilities(facilities);
        hotel.setManager(manager);
        hotel.setDescription(description);
        
        boolean success = hotelDAO.addHotel(hotel);
        
        if (success) {
            request.getSession().setAttribute("message", "饭店添加成功！");
        } else {
            request.getSession().setAttribute("error", "饭店添加失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/hotel?action=list");
    }
    
    /**
     * 显示编辑饭店表单
     */
    private void showEditForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        Hotel hotel = hotelDAO.getHotelById(id);
        request.setAttribute("hotel", hotel);
        request.getRequestDispatcher("/hotel-edit.jsp").forward(request, response);
    }
    
    /**
     * 更新饭店信息
     */
    private void updateHotel(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        String name = request.getParameter("name");
        String address = request.getParameter("address");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        int starLevel = Integer.parseInt(request.getParameter("starLevel"));
        int roomCount = Integer.parseInt(request.getParameter("roomCount"));
        String facilities = request.getParameter("facilities");
        String manager = request.getParameter("manager");
        String status = request.getParameter("status");
        String description = request.getParameter("description");
        
        Hotel hotel = new Hotel();
        hotel.setId(id);
        hotel.setName(name);
        hotel.setAddress(address);
        hotel.setPhone(phone);
        hotel.setEmail(email);
        hotel.setStarLevel(starLevel);
        hotel.setRoomCount(roomCount);
        hotel.setFacilities(facilities);
        hotel.setManager(manager);
        hotel.setStatus(status);
        hotel.setDescription(description);
        
        boolean success = hotelDAO.updateHotel(hotel);
        
        if (success) {
            request.getSession().setAttribute("message", "饭店信息更新成功！");
        } else {
            request.getSession().setAttribute("error", "饭店信息更新失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/hotel?action=list");
    }
    
    /**
     * 删除饭店
     */
    private void deleteHotel(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        boolean success = hotelDAO.deleteHotel(id);
        
        if (success) {
            request.getSession().setAttribute("message", "饭店删除成功！");
        } else {
            request.getSession().setAttribute("error", "饭店删除失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/hotel?action=list");
    }
    
    /**
     * 搜索饭店
     */
    private void searchHotels(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String keyword = request.getParameter("keyword");
        String starLevelStr = request.getParameter("starLevel");
        List<Hotel> hotels;
        
        if (starLevelStr != null && !starLevelStr.trim().isEmpty()) {
            // 按星级搜索
            int starLevel = Integer.parseInt(starLevelStr);
            hotels = hotelDAO.getHotelsByStarLevel(starLevel);
        } else if (keyword != null && !keyword.trim().isEmpty()) {
            // 按名称搜索
            hotels = hotelDAO.searchHotelsByName(keyword);
        } else {
            // 显示所有
            hotels = hotelDAO.getAllHotels();
        }
        
        request.setAttribute("hotels", hotels);
        request.setAttribute("keyword", keyword);
        request.setAttribute("starLevel", starLevelStr);
        request.getRequestDispatcher("/hotel-list.jsp").forward(request, response);
    }
}
