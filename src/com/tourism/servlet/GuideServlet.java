package com.tourism.servlet;

import com.tourism.dao.GuideDAO;
import com.tourism.model.Guide;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 导游管理Servlet
 */
public class GuideServlet extends HttpServlet {
    private GuideDAO guideDAO = new GuideDAO();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String action = request.getParameter("action");
        
        if (action == null) {
            action = "list";
        }
        
        switch (action) {
            case "list":
                listGuides(request, response);
                break;
            case "add":
                showAddForm(request, response);
                break;
            case "edit":
                showEditForm(request, response);
                break;
            case "delete":
                deleteGuide(request, response);
                break;
            case "search":
                searchGuides(request, response);
                break;
            default:
                listGuides(request, response);
                break;
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        String action = request.getParameter("action");
        
        if ("add".equals(action)) {
            addGuide(request, response);
        } else if ("update".equals(action)) {
            updateGuide(request, response);
        }
    }
    
    /**
     * 显示导游列表
     */
    private void listGuides(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        List<Guide> guides = guideDAO.getAllGuides();
        request.setAttribute("guides", guides);
        request.getRequestDispatcher("/guide-list.jsp").forward(request, response);
    }
    
    /**
     * 显示添加导游表单
     */
    private void showAddForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.getRequestDispatcher("/guide-add.jsp").forward(request, response);
    }
    
    /**
     * 添加导游
     */
    private void addGuide(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String name = request.getParameter("name");
        String idCard = request.getParameter("idCard");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String licenseNumber = request.getParameter("licenseNumber");
        String languages = request.getParameter("languages");
        String specialties = request.getParameter("specialties");
        String experienceYearsStr = request.getParameter("experienceYears");
        String description = request.getParameter("description");
        
        // 验证必填字段
        if (name == null || name.trim().isEmpty() || 
            idCard == null || idCard.trim().isEmpty() || 
            phone == null || phone.trim().isEmpty() || 
            licenseNumber == null || licenseNumber.trim().isEmpty()) {
            request.getSession().setAttribute("error", "请填写所有必填字段！");
            response.sendRedirect(request.getContextPath() + "/guide?action=add");
            return;
        }
        
        // 检查身份证号是否已存在
        if (guideDAO.isIdCardExists(idCard, 0)) {
            request.getSession().setAttribute("error", "身份证号已存在，请检查后重新输入！");
            response.sendRedirect(request.getContextPath() + "/guide?action=add");
            return;
        }
        
        // 检查导游证号是否已存在
        if (guideDAO.isLicenseNumberExists(licenseNumber, 0)) {
            request.getSession().setAttribute("error", "导游证号已存在，请检查后重新输入！");
            response.sendRedirect(request.getContextPath() + "/guide?action=add");
            return;
        }
        
        int experienceYears = 0;
        try {
            if (experienceYearsStr != null && !experienceYearsStr.trim().isEmpty()) {
                experienceYears = Integer.parseInt(experienceYearsStr);
            }
        } catch (NumberFormatException e) {
            request.getSession().setAttribute("error", "从业年限必须是数字！");
            response.sendRedirect(request.getContextPath() + "/guide?action=add");
            return;
        }
        
        Guide guide = new Guide(name, idCard, phone, email, licenseNumber);
        guide.setLanguages(languages);
        guide.setSpecialties(specialties);
        guide.setExperience(experienceYears);
        guide.setDescription(description);
        
        boolean success = guideDAO.addGuide(guide);
        
        if (success) {
            request.getSession().setAttribute("message", "导游添加成功！");
        } else {
            request.getSession().setAttribute("error", "导游添加失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/guide?action=list");
    }
    
    /**
     * 显示编辑导游表单
     */
    private void showEditForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        Guide guide = guideDAO.getGuideById(id);
        request.setAttribute("guide", guide);
        request.getRequestDispatcher("/guide-edit.jsp").forward(request, response);
    }
    
    /**
     * 更新导游信息
     */
    private void updateGuide(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        String name = request.getParameter("name");
        String idCard = request.getParameter("idCard");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String licenseNumber = request.getParameter("licenseNumber");
        String languages = request.getParameter("languages");
        String specialties = request.getParameter("specialties");
        String experienceYearsStr = request.getParameter("experienceYears");
        String status = request.getParameter("status");
        String description = request.getParameter("description");
        
        // 验证必填字段
        if (name == null || name.trim().isEmpty() || 
            idCard == null || idCard.trim().isEmpty() || 
            phone == null || phone.trim().isEmpty() || 
            licenseNumber == null || licenseNumber.trim().isEmpty()) {
            request.getSession().setAttribute("error", "请填写所有必填字段！");
            response.sendRedirect(request.getContextPath() + "/guide?action=edit&id=" + id);
            return;
        }
        
        // 检查身份证号是否已存在（排除当前记录）
        if (guideDAO.isIdCardExists(idCard, id)) {
            request.getSession().setAttribute("error", "身份证号已存在，请检查后重新输入！");
            response.sendRedirect(request.getContextPath() + "/guide?action=edit&id=" + id);
            return;
        }
        
        // 检查导游证号是否已存在（排除当前记录）
        if (guideDAO.isLicenseNumberExists(licenseNumber, id)) {
            request.getSession().setAttribute("error", "导游证号已存在，请检查后重新输入！");
            response.sendRedirect(request.getContextPath() + "/guide?action=edit&id=" + id);
            return;
        }
        
        int experienceYears = 0;
        try {
            if (experienceYearsStr != null && !experienceYearsStr.trim().isEmpty()) {
                experienceYears = Integer.parseInt(experienceYearsStr);
            }
        } catch (NumberFormatException e) {
            request.getSession().setAttribute("error", "从业年限必须是数字！");
            response.sendRedirect(request.getContextPath() + "/guide?action=edit&id=" + id);
            return;
        }
        
        Guide guide = new Guide();
        guide.setId(id);
        guide.setName(name);
        guide.setIdCard(idCard);
        guide.setPhone(phone);
        guide.setEmail(email);
        guide.setLicenseNumber(licenseNumber);
        guide.setLanguages(languages);
        guide.setSpecialties(specialties);
        guide.setExperience(experienceYears);
        guide.setStatus(status);
        guide.setDescription(description);
        
        boolean success = guideDAO.updateGuide(guide);
        
        if (success) {
            request.getSession().setAttribute("message", "导游信息更新成功！");
        } else {
            request.getSession().setAttribute("error", "导游信息更新失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/guide?action=list");
    }
    
    /**
     * 删除导游
     */
    private void deleteGuide(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        boolean success = guideDAO.deleteGuide(id);
        
        if (success) {
            request.getSession().setAttribute("message", "导游删除成功！");
        } else {
            request.getSession().setAttribute("error", "导游删除失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/guide?action=list");
    }
    
    /**
     * 搜索导游
     */
    private void searchGuides(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String keyword = request.getParameter("keyword");
        String status = request.getParameter("status");
        String language = request.getParameter("language");
        List<Guide> guides;
        
        if (language != null && !language.trim().isEmpty()) {
            // 按语言搜索
            guides = guideDAO.getGuidesByLanguage(language);
        } else if (status != null && !status.trim().isEmpty()) {
            // 按状态搜索
            guides = guideDAO.getGuidesByStatus(status);
        } else if (keyword != null && !keyword.trim().isEmpty()) {
            // 按名称搜索
            guides = guideDAO.searchGuidesByName(keyword);
        } else {
            // 显示所有
            guides = guideDAO.getAllGuides();
        }
        
        request.setAttribute("guides", guides);
        request.setAttribute("keyword", keyword);
        request.setAttribute("status", status);
        request.setAttribute("language", language);
        request.getRequestDispatcher("/guide-list.jsp").forward(request, response);
    }
}
