package com.tourism.servlet;

import com.tourism.dao.ApartmentDAO;
import com.tourism.model.Apartment;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 公寓管理Servlet
 */
public class ApartmentServlet extends HttpServlet {
    private ApartmentDAO apartmentDAO = new ApartmentDAO();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String action = request.getParameter("action");
        
        if (action == null) {
            action = "list";
        }
        
        switch (action) {
            case "list":
                listApartments(request, response);
                break;
            case "add":
                showAddForm(request, response);
                break;
            case "edit":
                showEditForm(request, response);
                break;
            case "delete":
                deleteApartment(request, response);
                break;
            case "search":
                searchApartments(request, response);
                break;
            case "available":
                listAvailableApartments(request, response);
                break;
            default:
                listApartments(request, response);
                break;
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        String action = request.getParameter("action");
        
        if ("add".equals(action)) {
            addApartment(request, response);
        } else if ("update".equals(action)) {
            updateApartment(request, response);
        }
    }
    
    /**
     * 显示公寓列表
     */
    private void listApartments(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        List<Apartment> apartments = apartmentDAO.getAllApartments();
        request.setAttribute("apartments", apartments);
        request.getRequestDispatcher("/apartment-list.jsp").forward(request, response);
    }
    
    /**
     * 显示可用公寓列表
     */
    private void listAvailableApartments(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        List<Apartment> apartments = apartmentDAO.getAvailableApartments();
        request.setAttribute("apartments", apartments);
        request.setAttribute("availableOnly", true);
        request.getRequestDispatcher("/apartment-list.jsp").forward(request, response);
    }
    
    /**
     * 显示添加公寓表单
     */
    private void showAddForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.getRequestDispatcher("/apartment-add.jsp").forward(request, response);
    }
    
    /**
     * 添加公寓
     */
    private void addApartment(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String name = request.getParameter("name");
        String address = request.getParameter("address");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String roomType = request.getParameter("roomType");
        String roomCountStr = request.getParameter("roomCount");
        String pricePerNightStr = request.getParameter("pricePerNight");
        String facilities = request.getParameter("facilities");
        String manager = request.getParameter("manager");
        String description = request.getParameter("description");
        
        // 验证必填字段
        if (name == null || name.trim().isEmpty() || 
            address == null || address.trim().isEmpty() || 
            phone == null || phone.trim().isEmpty() || 
            roomType == null || roomType.trim().isEmpty()) {
            request.getSession().setAttribute("error", "请填写所有必填字段！");
            response.sendRedirect(request.getContextPath() + "/apartment?action=add");
            return;
        }
        
        int roomCount = 0;
        double pricePerNight = 0.0;
        
        try {
            if (roomCountStr != null && !roomCountStr.trim().isEmpty()) {
                roomCount = Integer.parseInt(roomCountStr);
            }
            if (pricePerNightStr != null && !pricePerNightStr.trim().isEmpty()) {
                pricePerNight = Double.parseDouble(pricePerNightStr);
            }
        } catch (NumberFormatException e) {
            request.getSession().setAttribute("error", "房间数量和价格必须是有效数字！");
            response.sendRedirect(request.getContextPath() + "/apartment?action=add");
            return;
        }
        
        if (roomCount <= 0) {
            request.getSession().setAttribute("error", "房间数量必须大于0！");
            response.sendRedirect(request.getContextPath() + "/apartment?action=add");
            return;
        }
        
        if (pricePerNight < 0) {
            request.getSession().setAttribute("error", "价格不能为负数！");
            response.sendRedirect(request.getContextPath() + "/apartment?action=add");
            return;
        }
        
        Apartment apartment = new Apartment(name, address, phone, email, roomType, roomCount, pricePerNight);
        apartment.setFacilities(facilities);
        apartment.setManager(manager);
        apartment.setDescription(description);
        
        boolean success = apartmentDAO.addApartment(apartment);
        
        if (success) {
            request.getSession().setAttribute("message", "公寓添加成功！");
        } else {
            request.getSession().setAttribute("error", "公寓添加失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/apartment?action=list");
    }
    
    /**
     * 显示编辑公寓表单
     */
    private void showEditForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        Apartment apartment = apartmentDAO.getApartmentById(id);
        request.setAttribute("apartment", apartment);
        request.getRequestDispatcher("/apartment-edit.jsp").forward(request, response);
    }
    
    /**
     * 更新公寓信息
     */
    private void updateApartment(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        String name = request.getParameter("name");
        String address = request.getParameter("address");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String roomType = request.getParameter("roomType");
        String roomCountStr = request.getParameter("roomCount");
        String pricePerNightStr = request.getParameter("pricePerNight");
        String facilities = request.getParameter("facilities");
        String manager = request.getParameter("manager");
        String status = request.getParameter("status");
        String description = request.getParameter("description");
        
        // 验证必填字段
        if (name == null || name.trim().isEmpty() || 
            address == null || address.trim().isEmpty() || 
            phone == null || phone.trim().isEmpty() || 
            roomType == null || roomType.trim().isEmpty()) {
            request.getSession().setAttribute("error", "请填写所有必填字段！");
            response.sendRedirect(request.getContextPath() + "/apartment?action=edit&id=" + id);
            return;
        }
        
        int roomCount = 0;
        double pricePerNight = 0.0;
        
        try {
            if (roomCountStr != null && !roomCountStr.trim().isEmpty()) {
                roomCount = Integer.parseInt(roomCountStr);
            }
            if (pricePerNightStr != null && !pricePerNightStr.trim().isEmpty()) {
                pricePerNight = Double.parseDouble(pricePerNightStr);
            }
        } catch (NumberFormatException e) {
            request.getSession().setAttribute("error", "房间数量和价格必须是有效数字！");
            response.sendRedirect(request.getContextPath() + "/apartment?action=edit&id=" + id);
            return;
        }
        
        if (roomCount <= 0) {
            request.getSession().setAttribute("error", "房间数量必须大于0！");
            response.sendRedirect(request.getContextPath() + "/apartment?action=edit&id=" + id);
            return;
        }
        
        if (pricePerNight < 0) {
            request.getSession().setAttribute("error", "价格不能为负数！");
            response.sendRedirect(request.getContextPath() + "/apartment?action=edit&id=" + id);
            return;
        }
        
        Apartment apartment = new Apartment();
        apartment.setId(id);
        apartment.setName(name);
        apartment.setAddress(address);
        apartment.setPhone(phone);
        apartment.setEmail(email);
        apartment.setRoomType(roomType);
        apartment.setRoomCount(roomCount);
        apartment.setPricePerNight(pricePerNight);
        apartment.setFacilities(facilities);
        apartment.setManager(manager);
        apartment.setStatus(status);
        apartment.setDescription(description);
        
        boolean success = apartmentDAO.updateApartment(apartment);
        
        if (success) {
            request.getSession().setAttribute("message", "公寓信息更新成功！");
        } else {
            request.getSession().setAttribute("error", "公寓信息更新失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/apartment?action=list");
    }
    
    /**
     * 删除公寓
     */
    private void deleteApartment(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        boolean success = apartmentDAO.deleteApartment(id);
        
        if (success) {
            request.getSession().setAttribute("message", "公寓删除成功！");
        } else {
            request.getSession().setAttribute("error", "公寓删除失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/apartment?action=list");
    }
    
    /**
     * 搜索公寓
     */
    private void searchApartments(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String keyword = request.getParameter("keyword");
        String roomType = request.getParameter("roomType");
        String status = request.getParameter("status");
        String minPriceStr = request.getParameter("minPrice");
        String maxPriceStr = request.getParameter("maxPrice");
        
        List<Apartment> apartments;
        
        // 价格范围搜索
        if ((minPriceStr != null && !minPriceStr.trim().isEmpty()) || 
            (maxPriceStr != null && !maxPriceStr.trim().isEmpty())) {
            try {
                double minPrice = (minPriceStr != null && !minPriceStr.trim().isEmpty()) ? 
                    Double.parseDouble(minPriceStr) : 0.0;
                double maxPrice = (maxPriceStr != null && !maxPriceStr.trim().isEmpty()) ? 
                    Double.parseDouble(maxPriceStr) : Double.MAX_VALUE;
                apartments = apartmentDAO.getApartmentsByPriceRange(minPrice, maxPrice);
            } catch (NumberFormatException e) {
                request.getSession().setAttribute("error", "价格必须是有效数字！");
                apartments = apartmentDAO.getAllApartments();
            }
        }
        // 房间类型搜索
        else if (roomType != null && !roomType.trim().isEmpty()) {
            apartments = apartmentDAO.getApartmentsByRoomType(roomType);
        }
        // 状态搜索
        else if (status != null && !status.trim().isEmpty()) {
            apartments = apartmentDAO.getApartmentsByStatus(status);
        }
        // 名称搜索
        else if (keyword != null && !keyword.trim().isEmpty()) {
            apartments = apartmentDAO.searchApartmentsByName(keyword);
        }
        // 显示所有
        else {
            apartments = apartmentDAO.getAllApartments();
        }
        
        request.setAttribute("apartments", apartments);
        request.setAttribute("keyword", keyword);
        request.setAttribute("roomType", roomType);
        request.setAttribute("status", status);
        request.setAttribute("minPrice", minPriceStr);
        request.setAttribute("maxPrice", maxPriceStr);
        request.getRequestDispatcher("/apartment-list.jsp").forward(request, response);
    }
}
