package com.tourism.servlet;

import com.tourism.util.DatabaseUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 数据库连接测试Servlet
 */
@WebServlet("/database-test")
public class DatabaseTestServlet extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html lang='zh-CN'>");
        out.println("<head>");
        out.println("<meta charset='UTF-8'>");
        out.println("<title>数据库连接测试</title>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 20px; }");
        out.println(".success { color: green; }");
        out.println(".error { color: red; }");
        out.println(".info { color: blue; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<h1>数据库连接测试</h1>");
        
        // 测试数据库连接
        try {
            out.println("<h2>连接测试</h2>");
            Connection conn = DatabaseUtil.getConnection();
            out.println("<p class='success'>✅ 数据库连接成功！</p>");
            
            // 显示数据库信息
            DatabaseMetaData metaData = conn.getMetaData();
            out.println("<h2>数据库信息</h2>");
            out.println("<p><strong>数据库产品:</strong> " + metaData.getDatabaseProductName() + "</p>");
            out.println("<p><strong>数据库版本:</strong> " + metaData.getDatabaseProductVersion() + "</p>");
            out.println("<p><strong>驱动名称:</strong> " + metaData.getDriverName() + "</p>");
            out.println("<p><strong>驱动版本:</strong> " + metaData.getDriverVersion() + "</p>");
            out.println("<p><strong>连接URL:</strong> " + metaData.getURL() + "</p>");
            out.println("<p><strong>用户名:</strong> " + metaData.getUserName() + "</p>");
            
            // 检查表是否存在
            out.println("<h2>数据表检查</h2>");
            String[] tableNames = {"tourists", "hotels", "travel_agencies", "guides", "apartments", "complaints", "enterprises"};
            
            for (String tableName : tableNames) {
                ResultSet tables = metaData.getTables(null, null, tableName, null);
                if (tables.next()) {
                    out.println("<p class='success'>✅ " + tableName + " 表存在</p>");
                } else {
                    out.println("<p class='error'>❌ " + tableName + " 表不存在</p>");
                }
                tables.close();
            }
            
            conn.close();
            
        } catch (SQLException e) {
            out.println("<p class='error'>❌ 数据库连接失败: " + e.getMessage() + "</p>");
            out.println("<h2>错误详情</h2>");
            out.println("<pre>" + getStackTrace(e) + "</pre>");
        }
        
        // 显示配置信息
        out.println("<h2>配置信息</h2>");
        out.println("<pre>" + DatabaseUtil.getDatabaseInfo() + "</pre>");
        
        // 提供解决方案
        out.println("<h2>常见问题解决</h2>");
        out.println("<ol>");
        out.println("<li><strong>连接被拒绝:</strong> 检查MySQL服务是否启动</li>");
        out.println("<li><strong>认证失败:</strong> 检查用户名和密码是否正确</li>");
        out.println("<li><strong>数据库不存在:</strong> 创建tourism_system数据库</li>");
        out.println("<li><strong>表不存在:</strong> 运行database/init.sql脚本</li>");
        out.println("</ol>");
        
        out.println("<h2>快速操作</h2>");
        out.println("<p><a href='" + request.getContextPath() + "/database-test'>刷新测试</a></p>");
        out.println("<p><a href='" + request.getContextPath() + "/'>返回首页</a></p>");
        out.println("<p><a href='" + request.getContextPath() + "/tourist'>测试游客管理</a></p>");
        
        out.println("</body>");
        out.println("</html>");
    }
    
    private String getStackTrace(Exception e) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.toString();
    }
}
