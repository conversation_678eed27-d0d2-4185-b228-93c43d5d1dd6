package com.tourism.servlet;

import com.tourism.dao.ComplaintDAO;
import com.tourism.model.Complaint;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.List;

/**
 * 投诉管理Servlet
 */
public class ComplaintServlet extends HttpServlet {
    private ComplaintDAO complaintDAO = new ComplaintDAO();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String action = request.getParameter("action");
        
        if (action == null) {
            action = "list";
        }
        
        switch (action) {
            case "list":
                listComplaints(request, response);
                break;
            case "add":
                showAddForm(request, response);
                break;
            case "edit":
                showEditForm(request, response);
                break;
            case "handle":
                showHandleForm(request, response);
                break;
            case "delete":
                deleteComplaint(request, response);
                break;
            case "search":
                searchComplaints(request, response);
                break;
            case "pending":
                listPendingComplaints(request, response);
                break;
            case "statistics":
                showStatistics(request, response);
                break;
            default:
                listComplaints(request, response);
                break;
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        String action = request.getParameter("action");
        
        if ("add".equals(action)) {
            addComplaint(request, response);
        } else if ("update".equals(action)) {
            updateComplaint(request, response);
        } else if ("handle".equals(action)) {
            handleComplaint(request, response);
        }
    }
    
    /**
     * 显示投诉列表
     */
    private void listComplaints(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        List<Complaint> complaints = complaintDAO.getAllComplaints();
        request.setAttribute("complaints", complaints);
        request.getRequestDispatcher("/complaint-list.jsp").forward(request, response);
    }
    
    /**
     * 显示待处理投诉列表
     */
    private void listPendingComplaints(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        List<Complaint> complaints = complaintDAO.getPendingComplaints();
        request.setAttribute("complaints", complaints);
        request.setAttribute("pendingOnly", true);
        request.getRequestDispatcher("/complaint-list.jsp").forward(request, response);
    }
    
    /**
     * 显示添加投诉表单
     */
    private void showAddForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.getRequestDispatcher("/complaint-add.jsp").forward(request, response);
    }
    
    /**
     * 添加投诉
     */
    private void addComplaint(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String customerName = request.getParameter("customerName");
        String customerPhone = request.getParameter("customerPhone");
        String customerEmail = request.getParameter("customerEmail");
        String complaintType = request.getParameter("complaintType");
        String complaintContent = request.getParameter("complaintContent");
        String relatedService = request.getParameter("relatedService");
        String priority = request.getParameter("priority");
        String description = request.getParameter("description");
        
        // 验证必填字段
        if (customerName == null || customerName.trim().isEmpty() || 
            customerPhone == null || customerPhone.trim().isEmpty() || 
            complaintType == null || complaintType.trim().isEmpty() || 
            complaintContent == null || complaintContent.trim().isEmpty()) {
            request.getSession().setAttribute("error", "请填写所有必填字段！");
            response.sendRedirect(request.getContextPath() + "/complaint?action=add");
            return;
        }
        
        Complaint complaint = new Complaint();
        complaint.setCustomerName(customerName);
        complaint.setCustomerPhone(customerPhone);
        complaint.setCustomerEmail(customerEmail);
        complaint.setComplaintType(complaintType);
        complaint.setComplaintContent(complaintContent);
        complaint.setRelatedService(relatedService);
        complaint.setComplaintDate(new Timestamp(System.currentTimeMillis()));
        complaint.setStatus("待处理");
        complaint.setPriority(priority != null && !priority.trim().isEmpty() ? priority : "普通");
        complaint.setDescription(description);
        
        boolean success = complaintDAO.addComplaint(complaint);
        
        if (success) {
            request.getSession().setAttribute("message", "投诉提交成功！我们会尽快处理。");
        } else {
            request.getSession().setAttribute("error", "投诉提交失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/complaint?action=list");
    }
    
    /**
     * 显示编辑投诉表单
     */
    private void showEditForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        Complaint complaint = complaintDAO.getComplaintById(id);
        request.setAttribute("complaint", complaint);
        request.getRequestDispatcher("/complaint-edit.jsp").forward(request, response);
    }
    
    /**
     * 显示处理投诉表单
     */
    private void showHandleForm(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        Complaint complaint = complaintDAO.getComplaintById(id);
        request.setAttribute("complaint", complaint);
        request.getRequestDispatcher("/complaint-handle.jsp").forward(request, response);
    }
    
    /**
     * 更新投诉信息
     */
    private void updateComplaint(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        String customerName = request.getParameter("customerName");
        String customerPhone = request.getParameter("customerPhone");
        String customerEmail = request.getParameter("customerEmail");
        String complaintType = request.getParameter("complaintType");
        String complaintContent = request.getParameter("complaintContent");
        String relatedService = request.getParameter("relatedService");
        String status = request.getParameter("status");
        String priority = request.getParameter("priority");
        String description = request.getParameter("description");
        
        // 验证必填字段
        if (customerName == null || customerName.trim().isEmpty() || 
            customerPhone == null || customerPhone.trim().isEmpty() || 
            complaintType == null || complaintType.trim().isEmpty() || 
            complaintContent == null || complaintContent.trim().isEmpty()) {
            request.getSession().setAttribute("error", "请填写所有必填字段！");
            response.sendRedirect(request.getContextPath() + "/complaint?action=edit&id=" + id);
            return;
        }
        
        Complaint complaint = complaintDAO.getComplaintById(id);
        if (complaint != null) {
            complaint.setCustomerName(customerName);
            complaint.setCustomerPhone(customerPhone);
            complaint.setCustomerEmail(customerEmail);
            complaint.setComplaintType(complaintType);
            complaint.setComplaintContent(complaintContent);
            complaint.setRelatedService(relatedService);
            complaint.setStatus(status);
            complaint.setPriority(priority);
            complaint.setDescription(description);
            
            boolean success = complaintDAO.updateComplaint(complaint);
            
            if (success) {
                request.getSession().setAttribute("message", "投诉信息更新成功！");
            } else {
                request.getSession().setAttribute("error", "投诉信息更新失败！");
            }
        }
        
        response.sendRedirect(request.getContextPath() + "/complaint?action=list");
    }
    
    /**
     * 处理投诉
     */
    private void handleComplaint(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        String handler = request.getParameter("handler");
        String handleResult = request.getParameter("handleResult");
        String status = request.getParameter("status");
        
        // 验证必填字段
        if (handler == null || handler.trim().isEmpty() || 
            handleResult == null || handleResult.trim().isEmpty() || 
            status == null || status.trim().isEmpty()) {
            request.getSession().setAttribute("error", "请填写所有处理信息！");
            response.sendRedirect(request.getContextPath() + "/complaint?action=handle&id=" + id);
            return;
        }
        
        Complaint complaint = complaintDAO.getComplaintById(id);
        if (complaint != null) {
            complaint.setHandler(handler);
            complaint.setHandleResult(handleResult);
            complaint.setStatus(status);
            complaint.setHandleDate(new Timestamp(System.currentTimeMillis()));
            
            boolean success = complaintDAO.updateComplaint(complaint);
            
            if (success) {
                request.getSession().setAttribute("message", "投诉处理完成！");
            } else {
                request.getSession().setAttribute("error", "投诉处理失败！");
            }
        }
        
        response.sendRedirect(request.getContextPath() + "/complaint?action=list");
    }
    
    /**
     * 删除投诉
     */
    private void deleteComplaint(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        boolean success = complaintDAO.deleteComplaint(id);
        
        if (success) {
            request.getSession().setAttribute("message", "投诉删除成功！");
        } else {
            request.getSession().setAttribute("error", "投诉删除失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/complaint?action=list");
    }
    
    /**
     * 搜索投诉
     */
    private void searchComplaints(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String keyword = request.getParameter("keyword");
        String complaintType = request.getParameter("complaintType");
        String status = request.getParameter("status");
        String priority = request.getParameter("priority");
        
        List<Complaint> complaints;
        
        if (priority != null && !priority.trim().isEmpty()) {
            // 按优先级搜索
            complaints = complaintDAO.getComplaintsByPriority(priority);
        } else if (status != null && !status.trim().isEmpty()) {
            // 按状态搜索
            complaints = complaintDAO.getComplaintsByStatus(status);
        } else if (complaintType != null && !complaintType.trim().isEmpty()) {
            // 按投诉类型搜索
            complaints = complaintDAO.getComplaintsByType(complaintType);
        } else if (keyword != null && !keyword.trim().isEmpty()) {
            // 按客户姓名搜索
            complaints = complaintDAO.searchComplaintsByCustomerName(keyword);
        } else {
            // 显示所有
            complaints = complaintDAO.getAllComplaints();
        }
        
        request.setAttribute("complaints", complaints);
        request.setAttribute("keyword", keyword);
        request.setAttribute("complaintType", complaintType);
        request.setAttribute("status", status);
        request.setAttribute("priority", priority);
        request.getRequestDispatcher("/complaint-list.jsp").forward(request, response);
    }
    
    /**
     * 显示统计信息
     */
    private void showStatistics(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int pendingCount = complaintDAO.getComplaintCountByStatus("待处理");
        int processingCount = complaintDAO.getComplaintCountByStatus("处理中");
        int resolvedCount = complaintDAO.getComplaintCountByStatus("已解决");
        int closedCount = complaintDAO.getComplaintCountByStatus("已关闭");
        
        request.setAttribute("pendingCount", pendingCount);
        request.setAttribute("processingCount", processingCount);
        request.setAttribute("resolvedCount", resolvedCount);
        request.setAttribute("closedCount", closedCount);
        request.getRequestDispatcher("/complaint-statistics.jsp").forward(request, response);
    }
}
