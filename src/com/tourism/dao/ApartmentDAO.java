package com.tourism.dao;

import com.tourism.model.Apartment;
import com.tourism.util.DatabaseUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 公寓数据访问对象
 */
public class ApartmentDAO {
    
    /**
     * 添加公寓
     */
    public boolean addApartment(Apartment apartment) {
        String sql = "INSERT INTO apartments (name, address, phone, email, room_type, room_count, price_per_night, facilities, manager, status, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int result = DatabaseUtil.executeUpdate(sql, 
            apartment.getName(), 
            apartment.getAddress(), 
            apartment.getPhone(), 
            apartment.getEmail(), 
            apartment.getRoomType(), 
            apartment.getRoomCount(), 
            apartment.getPricePerNight(), 
            apartment.getFacilities(), 
            apartment.getManager(), 
            apartment.getStatus(), 
            apartment.getDescription()
        );
        return result > 0;
    }
    
    /**
     * 根据ID删除公寓
     */
    public boolean deleteApartment(int id) {
        String sql = "DELETE FROM apartments WHERE id = ?";
        int result = DatabaseUtil.executeUpdate(sql, id);
        return result > 0;
    }
    
    /**
     * 更新公寓信息
     */
    public boolean updateApartment(Apartment apartment) {
        String sql = "UPDATE apartments SET name=?, address=?, phone=?, email=?, room_type=?, room_count=?, price_per_night=?, facilities=?, manager=?, status=?, description=? WHERE id=?";
        int result = DatabaseUtil.executeUpdate(sql, 
            apartment.getName(), 
            apartment.getAddress(), 
            apartment.getPhone(), 
            apartment.getEmail(), 
            apartment.getRoomType(), 
            apartment.getRoomCount(), 
            apartment.getPricePerNight(), 
            apartment.getFacilities(), 
            apartment.getManager(), 
            apartment.getStatus(), 
            apartment.getDescription(), 
            apartment.getId()
        );
        return result > 0;
    }
    
    /**
     * 根据ID查询公寓
     */
    public Apartment getApartmentById(int id) {
        String sql = "SELECT * FROM apartments WHERE id = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, id);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return extractApartmentFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return null;
    }
    
    /**
     * 查询所有公寓
     */
    public List<Apartment> getAllApartments() {
        String sql = "SELECT * FROM apartments ORDER BY price_per_night ASC, name ASC";
        List<Apartment> apartments = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                apartments.add(extractApartmentFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return apartments;
    }
    
    /**
     * 根据名称搜索公寓
     */
    public List<Apartment> searchApartmentsByName(String name) {
        String sql = "SELECT * FROM apartments WHERE name LIKE ? ORDER BY price_per_night ASC, name ASC";
        List<Apartment> apartments = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, "%" + name + "%");
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                apartments.add(extractApartmentFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return apartments;
    }
    
    /**
     * 根据房间类型查询公寓
     */
    public List<Apartment> getApartmentsByRoomType(String roomType) {
        String sql = "SELECT * FROM apartments WHERE room_type = ? ORDER BY price_per_night ASC, name ASC";
        List<Apartment> apartments = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, roomType);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                apartments.add(extractApartmentFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return apartments;
    }
    
    /**
     * 根据状态查询公寓
     */
    public List<Apartment> getApartmentsByStatus(String status) {
        String sql = "SELECT * FROM apartments WHERE status = ? ORDER BY price_per_night ASC, name ASC";
        List<Apartment> apartments = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, status);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                apartments.add(extractApartmentFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return apartments;
    }
    
    /**
     * 根据价格范围查询公寓
     */
    public List<Apartment> getApartmentsByPriceRange(double minPrice, double maxPrice) {
        String sql = "SELECT * FROM apartments WHERE price_per_night BETWEEN ? AND ? ORDER BY price_per_night ASC, name ASC";
        List<Apartment> apartments = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setDouble(1, minPrice);
            pstmt.setDouble(2, maxPrice);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                apartments.add(extractApartmentFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return apartments;
    }
    
    /**
     * 获取可用公寓（状态为可预订）
     */
    public List<Apartment> getAvailableApartments() {
        String sql = "SELECT * FROM apartments WHERE status = '可预订' ORDER BY price_per_night ASC, name ASC";
        List<Apartment> apartments = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                apartments.add(extractApartmentFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return apartments;
    }
    
    /**
     * 从ResultSet中提取Apartment对象
     */
    private Apartment extractApartmentFromResultSet(ResultSet rs) throws SQLException {
        Apartment apartment = new Apartment();
        apartment.setId(rs.getInt("id"));
        apartment.setName(rs.getString("name"));
        apartment.setAddress(rs.getString("address"));
        apartment.setPhone(rs.getString("phone"));
        apartment.setEmail(rs.getString("email"));
        apartment.setRoomType(rs.getString("room_type"));
        apartment.setRoomCount(rs.getInt("room_count"));
        apartment.setPricePerNight(rs.getDouble("price_per_night"));
        apartment.setFacilities(rs.getString("facilities"));
        apartment.setManager(rs.getString("manager"));
        apartment.setStatus(rs.getString("status"));
        apartment.setDescription(rs.getString("description"));
        return apartment;
    }
}
