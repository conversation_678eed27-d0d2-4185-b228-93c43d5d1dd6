package com.tourism.dao;

import com.tourism.model.Tourist;
import com.tourism.util.DatabaseUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 游客数据访问对象
 */
public class TouristDAO {
    
    /**
     * 添加游客
     */
    public boolean addTourist(Tourist tourist) {
        String sql = "INSERT INTO tourists (name, id_card, phone, email, address, register_date, status, remarks) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        int result = DatabaseUtil.executeUpdate(sql, 
            tourist.getName(), 
            tourist.getIdCard(), 
            tourist.getPhone(), 
            tourist.getEmail(), 
            tourist.getAddress(), 
            tourist.getRegisterDate(), 
            tourist.getStatus(), 
            tourist.getRemarks()
        );
        return result > 0;
    }
    
    /**
     * 根据ID删除游客
     */
    public boolean deleteTourist(int id) {
        String sql = "DELETE FROM tourists WHERE id = ?";
        int result = DatabaseUtil.executeUpdate(sql, id);
        return result > 0;
    }
    
    /**
     * 更新游客信息
     */
    public boolean updateTourist(Tourist tourist) {
        String sql = "UPDATE tourists SET name=?, id_card=?, phone=?, email=?, address=?, status=?, remarks=? WHERE id=?";
        int result = DatabaseUtil.executeUpdate(sql, 
            tourist.getName(), 
            tourist.getIdCard(), 
            tourist.getPhone(), 
            tourist.getEmail(), 
            tourist.getAddress(), 
            tourist.getStatus(), 
            tourist.getRemarks(), 
            tourist.getId()
        );
        return result > 0;
    }
    
    /**
     * 根据ID查询游客
     */
    public Tourist getTouristById(int id) {
        String sql = "SELECT * FROM tourists WHERE id = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, id);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return extractTouristFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return null;
    }
    
    /**
     * 查询所有游客
     */
    public List<Tourist> getAllTourists() {
        String sql = "SELECT * FROM tourists ORDER BY register_date DESC";
        List<Tourist> tourists = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                tourists.add(extractTouristFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return tourists;
    }
    
    /**
     * 根据姓名搜索游客
     */
    public List<Tourist> searchTouristsByName(String name) {
        String sql = "SELECT * FROM tourists WHERE name LIKE ? ORDER BY register_date DESC";
        List<Tourist> tourists = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, "%" + name + "%");
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                tourists.add(extractTouristFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return tourists;
    }
    
    /**
     * 从ResultSet中提取Tourist对象
     */
    private Tourist extractTouristFromResultSet(ResultSet rs) throws SQLException {
        Tourist tourist = new Tourist();
        tourist.setId(rs.getInt("id"));
        tourist.setName(rs.getString("name"));
        tourist.setIdCard(rs.getString("id_card"));
        tourist.setPhone(rs.getString("phone"));
        tourist.setEmail(rs.getString("email"));
        tourist.setAddress(rs.getString("address"));
        tourist.setRegisterDate(rs.getTimestamp("register_date"));
        tourist.setStatus(rs.getString("status"));
        tourist.setRemarks(rs.getString("remarks"));
        return tourist;
    }
}
