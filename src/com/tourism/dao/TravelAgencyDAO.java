package com.tourism.dao;

import com.tourism.model.TravelAgency;
import com.tourism.util.DatabaseUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 旅行社数据访问对象
 */
public class TravelAgencyDAO {
    
    /**
     * 添加旅行社
     */
    public boolean addTravelAgency(TravelAgency agency) {
        String sql = "INSERT INTO travel_agencies (name, address, phone, email, license_number, manager, business_scope, status, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int result = DatabaseUtil.executeUpdate(sql, 
            agency.getName(), 
            agency.getAddress(), 
            agency.getPhone(), 
            agency.getEmail(), 
            agency.getLicenseNumber(), 
            agency.getManager(), 
            agency.getBusinessScope(), 
            agency.getStatus(), 
            agency.getDescription()
        );
        return result > 0;
    }
    
    /**
     * 根据ID删除旅行社
     */
    public boolean deleteTravelAgency(int id) {
        String sql = "DELETE FROM travel_agencies WHERE id = ?";
        int result = DatabaseUtil.executeUpdate(sql, id);
        return result > 0;
    }
    
    /**
     * 更新旅行社信息
     */
    public boolean updateTravelAgency(TravelAgency agency) {
        String sql = "UPDATE travel_agencies SET name=?, address=?, phone=?, email=?, license_number=?, manager=?, business_scope=?, status=?, description=? WHERE id=?";
        int result = DatabaseUtil.executeUpdate(sql, 
            agency.getName(), 
            agency.getAddress(), 
            agency.getPhone(), 
            agency.getEmail(), 
            agency.getLicenseNumber(), 
            agency.getManager(), 
            agency.getBusinessScope(), 
            agency.getStatus(), 
            agency.getDescription(), 
            agency.getId()
        );
        return result > 0;
    }
    
    /**
     * 根据ID查询旅行社
     */
    public TravelAgency getTravelAgencyById(int id) {
        String sql = "SELECT * FROM travel_agencies WHERE id = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, id);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return extractTravelAgencyFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return null;
    }
    
    /**
     * 查询所有旅行社
     */
    public List<TravelAgency> getAllTravelAgencies() {
        String sql = "SELECT * FROM travel_agencies ORDER BY name ASC";
        List<TravelAgency> agencies = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                agencies.add(extractTravelAgencyFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return agencies;
    }
    
    /**
     * 根据名称搜索旅行社
     */
    public List<TravelAgency> searchTravelAgenciesByName(String name) {
        String sql = "SELECT * FROM travel_agencies WHERE name LIKE ? ORDER BY name ASC";
        List<TravelAgency> agencies = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, "%" + name + "%");
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                agencies.add(extractTravelAgencyFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return agencies;
    }
    
    /**
     * 根据状态查询旅行社
     */
    public List<TravelAgency> getTravelAgenciesByStatus(String status) {
        String sql = "SELECT * FROM travel_agencies WHERE status = ? ORDER BY name ASC";
        List<TravelAgency> agencies = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, status);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                agencies.add(extractTravelAgencyFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return agencies;
    }
    
    /**
     * 检查营业执照号是否已存在
     */
    public boolean isLicenseNumberExists(String licenseNumber, int excludeId) {
        String sql = "SELECT COUNT(*) FROM travel_agencies WHERE license_number = ? AND id != ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, licenseNumber);
            pstmt.setInt(2, excludeId);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return false;
    }
    
    /**
     * 从ResultSet中提取TravelAgency对象
     */
    private TravelAgency extractTravelAgencyFromResultSet(ResultSet rs) throws SQLException {
        TravelAgency agency = new TravelAgency();
        agency.setId(rs.getInt("id"));
        agency.setName(rs.getString("name"));
        agency.setAddress(rs.getString("address"));
        agency.setPhone(rs.getString("phone"));
        agency.setEmail(rs.getString("email"));
        agency.setLicenseNumber(rs.getString("license_number"));
        agency.setManager(rs.getString("manager"));
        agency.setBusinessScope(rs.getString("business_scope"));
        agency.setStatus(rs.getString("status"));
        agency.setDescription(rs.getString("description"));
        return agency;
    }
}
