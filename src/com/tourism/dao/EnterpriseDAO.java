package com.tourism.dao;

import com.tourism.model.Enterprise;
import com.tourism.util.DatabaseUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 企业档案数据访问对象
 */
public class EnterpriseDAO {
    
    /**
     * 添加企业档案
     */
    public boolean addEnterprise(Enterprise enterprise) {
        String sql = "INSERT INTO enterprises (name, type, license_number, legal_person, address, phone, email, business_scope, registration_date, status, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int result = DatabaseUtil.executeUpdate(sql, 
            enterprise.getName(), 
            enterprise.getType(), 
            enterprise.getLicenseNumber(), 
            enterprise.getLegalPerson(), 
            enterprise.getAddress(), 
            enterprise.getPhone(), 
            enterprise.getEmail(), 
            enterprise.getBusinessScope(), 
            enterprise.getRegistrationDate(), 
            enterprise.getStatus(), 
            enterprise.getDescription()
        );
        return result > 0;
    }
    
    /**
     * 根据ID删除企业档案
     */
    public boolean deleteEnterprise(int id) {
        String sql = "DELETE FROM enterprises WHERE id = ?";
        int result = DatabaseUtil.executeUpdate(sql, id);
        return result > 0;
    }
    
    /**
     * 更新企业档案信息
     */
    public boolean updateEnterprise(Enterprise enterprise) {
        String sql = "UPDATE enterprises SET name=?, type=?, license_number=?, legal_person=?, address=?, phone=?, email=?, business_scope=?, registration_date=?, status=?, description=? WHERE id=?";
        int result = DatabaseUtil.executeUpdate(sql, 
            enterprise.getName(), 
            enterprise.getType(), 
            enterprise.getLicenseNumber(), 
            enterprise.getLegalPerson(), 
            enterprise.getAddress(), 
            enterprise.getPhone(), 
            enterprise.getEmail(), 
            enterprise.getBusinessScope(), 
            enterprise.getRegistrationDate(), 
            enterprise.getStatus(), 
            enterprise.getDescription(), 
            enterprise.getId()
        );
        return result > 0;
    }
    
    /**
     * 根据ID查询企业档案
     */
    public Enterprise getEnterpriseById(int id) {
        String sql = "SELECT * FROM enterprises WHERE id = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, id);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return extractEnterpriseFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return null;
    }
    
    /**
     * 查询所有企业档案
     */
    public List<Enterprise> getAllEnterprises() {
        String sql = "SELECT * FROM enterprises ORDER BY created_at DESC";
        List<Enterprise> enterprises = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                enterprises.add(extractEnterpriseFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return enterprises;
    }
    
    /**
     * 根据企业名称搜索
     */
    public List<Enterprise> searchEnterprisesByName(String name) {
        String sql = "SELECT * FROM enterprises WHERE name LIKE ? ORDER BY created_at DESC";
        List<Enterprise> enterprises = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, "%" + name + "%");
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                enterprises.add(extractEnterpriseFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return enterprises;
    }
    
    /**
     * 根据企业类型查询
     */
    public List<Enterprise> getEnterprisesByType(String type) {
        String sql = "SELECT * FROM enterprises WHERE type = ? ORDER BY created_at DESC";
        List<Enterprise> enterprises = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, type);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                enterprises.add(extractEnterpriseFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return enterprises;
    }
    
    /**
     * 根据状态查询企业档案
     */
    public List<Enterprise> getEnterprisesByStatus(String status) {
        String sql = "SELECT * FROM enterprises WHERE status = ? ORDER BY created_at DESC";
        List<Enterprise> enterprises = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, status);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                enterprises.add(extractEnterpriseFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return enterprises;
    }
    
    /**
     * 根据营业执照号查询企业
     */
    public Enterprise getEnterpriseByLicenseNumber(String licenseNumber) {
        String sql = "SELECT * FROM enterprises WHERE license_number = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, licenseNumber);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return extractEnterpriseFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return null;
    }
    
    /**
     * 获取正常状态的企业数量
     */
    public int getActiveEnterpriseCount() {
        String sql = "SELECT COUNT(*) FROM enterprises WHERE status = '正常'";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return 0;
    }
    
    /**
     * 获取各类型企业数量统计
     */
    public int getEnterpriseCountByType(String type) {
        String sql = "SELECT COUNT(*) FROM enterprises WHERE type = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, type);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return 0;
    }
    
    /**
     * 检查营业执照号是否已存在
     */
    public boolean isLicenseNumberExists(String licenseNumber, int excludeId) {
        String sql = "SELECT COUNT(*) FROM enterprises WHERE license_number = ? AND id != ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, licenseNumber);
            pstmt.setInt(2, excludeId);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return false;
    }
    
    /**
     * 从ResultSet中提取Enterprise对象
     */
    private Enterprise extractEnterpriseFromResultSet(ResultSet rs) throws SQLException {
        Enterprise enterprise = new Enterprise();
        enterprise.setId(rs.getInt("id"));
        enterprise.setName(rs.getString("name"));
        enterprise.setType(rs.getString("type"));
        enterprise.setLicenseNumber(rs.getString("license_number"));
        enterprise.setLegalPerson(rs.getString("legal_person"));
        enterprise.setAddress(rs.getString("address"));
        enterprise.setPhone(rs.getString("phone"));
        enterprise.setEmail(rs.getString("email"));
        enterprise.setBusinessScope(rs.getString("business_scope"));
        enterprise.setRegistrationDate(rs.getDate("registration_date"));
        enterprise.setStatus(rs.getString("status"));
        enterprise.setDescription(rs.getString("description"));
        enterprise.setCreatedAt(rs.getTimestamp("created_at"));
        enterprise.setUpdatedAt(rs.getTimestamp("updated_at"));
        return enterprise;
    }
}
