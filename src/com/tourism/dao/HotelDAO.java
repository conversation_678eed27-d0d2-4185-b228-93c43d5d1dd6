package com.tourism.dao;

import com.tourism.model.Hotel;
import com.tourism.util.DatabaseUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 星级饭店数据访问对象
 */
public class HotelDAO {
    
    /**
     * 添加饭店
     */
    public boolean addHotel(Hotel hotel) {
        String sql = "INSERT INTO hotels (name, address, phone, email, star_level, room_count, facilities, manager, status, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int result = DatabaseUtil.executeUpdate(sql, 
            hotel.getName(), 
            hotel.getAddress(), 
            hotel.getPhone(), 
            hotel.getEmail(), 
            hotel.getStarLevel(), 
            hotel.getRoomCount(), 
            hotel.getFacilities(), 
            hotel.getManager(), 
            hotel.getStatus(), 
            hotel.getDescription()
        );
        return result > 0;
    }
    
    /**
     * 根据ID删除饭店
     */
    public boolean deleteHotel(int id) {
        String sql = "DELETE FROM hotels WHERE id = ?";
        int result = DatabaseUtil.executeUpdate(sql, id);
        return result > 0;
    }
    
    /**
     * 更新饭店信息
     */
    public boolean updateHotel(Hotel hotel) {
        String sql = "UPDATE hotels SET name=?, address=?, phone=?, email=?, star_level=?, room_count=?, facilities=?, manager=?, status=?, description=? WHERE id=?";
        int result = DatabaseUtil.executeUpdate(sql, 
            hotel.getName(), 
            hotel.getAddress(), 
            hotel.getPhone(), 
            hotel.getEmail(), 
            hotel.getStarLevel(), 
            hotel.getRoomCount(), 
            hotel.getFacilities(), 
            hotel.getManager(), 
            hotel.getStatus(), 
            hotel.getDescription(), 
            hotel.getId()
        );
        return result > 0;
    }
    
    /**
     * 根据ID查询饭店
     */
    public Hotel getHotelById(int id) {
        String sql = "SELECT * FROM hotels WHERE id = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, id);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return extractHotelFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return null;
    }
    
    /**
     * 查询所有饭店
     */
    public List<Hotel> getAllHotels() {
        String sql = "SELECT * FROM hotels ORDER BY star_level DESC, name ASC";
        List<Hotel> hotels = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                hotels.add(extractHotelFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return hotels;
    }
    
    /**
     * 根据名称搜索饭店
     */
    public List<Hotel> searchHotelsByName(String name) {
        String sql = "SELECT * FROM hotels WHERE name LIKE ? ORDER BY star_level DESC, name ASC";
        List<Hotel> hotels = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, "%" + name + "%");
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                hotels.add(extractHotelFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return hotels;
    }
    
    /**
     * 根据星级查询饭店
     */
    public List<Hotel> getHotelsByStarLevel(int starLevel) {
        String sql = "SELECT * FROM hotels WHERE star_level = ? ORDER BY name ASC";
        List<Hotel> hotels = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, starLevel);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                hotels.add(extractHotelFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return hotels;
    }
    
    /**
     * 从ResultSet中提取Hotel对象
     */
    private Hotel extractHotelFromResultSet(ResultSet rs) throws SQLException {
        Hotel hotel = new Hotel();
        hotel.setId(rs.getInt("id"));
        hotel.setName(rs.getString("name"));
        hotel.setAddress(rs.getString("address"));
        hotel.setPhone(rs.getString("phone"));
        hotel.setEmail(rs.getString("email"));
        hotel.setStarLevel(rs.getInt("star_level"));
        hotel.setRoomCount(rs.getInt("room_count"));
        hotel.setFacilities(rs.getString("facilities"));
        hotel.setManager(rs.getString("manager"));
        hotel.setStatus(rs.getString("status"));
        hotel.setDescription(rs.getString("description"));
        return hotel;
    }
}
