package com.tourism.dao;

import com.tourism.model.Complaint;
import com.tourism.util.DatabaseUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 投诉数据访问对象
 */
public class ComplaintDAO {
    
    /**
     * 添加投诉
     */
    public boolean addComplaint(Complaint complaint) {
        String sql = "INSERT INTO complaints (customer_name, customer_phone, customer_email, complaint_type, complaint_content, related_service, complaint_date, status, priority, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int result = DatabaseUtil.executeUpdate(sql, 
            complaint.getCustomerName(), 
            complaint.getCustomerPhone(), 
            complaint.getCustomerEmail(), 
            complaint.getComplaintType(), 
            complaint.getComplaintContent(), 
            complaint.getRelatedService(), 
            complaint.getComplaintDate(), 
            complaint.getStatus(), 
            complaint.getPriority(), 
            complaint.getDescription()
        );
        return result > 0;
    }
    
    /**
     * 根据ID删除投诉
     */
    public boolean deleteComplaint(int id) {
        String sql = "DELETE FROM complaints WHERE id = ?";
        int result = DatabaseUtil.executeUpdate(sql, id);
        return result > 0;
    }
    
    /**
     * 更新投诉信息
     */
    public boolean updateComplaint(Complaint complaint) {
        String sql = "UPDATE complaints SET customer_name=?, customer_phone=?, customer_email=?, complaint_type=?, complaint_content=?, related_service=?, status=?, priority=?, handler=?, handle_date=?, handle_result=?, description=? WHERE id=?";
        int result = DatabaseUtil.executeUpdate(sql, 
            complaint.getCustomerName(), 
            complaint.getCustomerPhone(), 
            complaint.getCustomerEmail(), 
            complaint.getComplaintType(), 
            complaint.getComplaintContent(), 
            complaint.getRelatedService(), 
            complaint.getStatus(), 
            complaint.getPriority(), 
            complaint.getHandler(), 
            complaint.getHandleDate(), 
            complaint.getHandleResult(), 
            complaint.getDescription(), 
            complaint.getId()
        );
        return result > 0;
    }
    
    /**
     * 根据ID查询投诉
     */
    public Complaint getComplaintById(int id) {
        String sql = "SELECT * FROM complaints WHERE id = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, id);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return extractComplaintFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return null;
    }
    
    /**
     * 查询所有投诉
     */
    public List<Complaint> getAllComplaints() {
        String sql = "SELECT * FROM complaints ORDER BY complaint_date DESC, priority DESC";
        List<Complaint> complaints = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                complaints.add(extractComplaintFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return complaints;
    }
    
    /**
     * 根据客户姓名搜索投诉
     */
    public List<Complaint> searchComplaintsByCustomerName(String customerName) {
        String sql = "SELECT * FROM complaints WHERE customer_name LIKE ? ORDER BY complaint_date DESC";
        List<Complaint> complaints = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, "%" + customerName + "%");
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                complaints.add(extractComplaintFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return complaints;
    }
    
    /**
     * 根据投诉类型查询投诉
     */
    public List<Complaint> getComplaintsByType(String complaintType) {
        String sql = "SELECT * FROM complaints WHERE complaint_type = ? ORDER BY complaint_date DESC";
        List<Complaint> complaints = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, complaintType);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                complaints.add(extractComplaintFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return complaints;
    }
    
    /**
     * 根据状态查询投诉
     */
    public List<Complaint> getComplaintsByStatus(String status) {
        String sql = "SELECT * FROM complaints WHERE status = ? ORDER BY complaint_date DESC, priority DESC";
        List<Complaint> complaints = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, status);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                complaints.add(extractComplaintFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return complaints;
    }
    
    /**
     * 根据优先级查询投诉
     */
    public List<Complaint> getComplaintsByPriority(String priority) {
        String sql = "SELECT * FROM complaints WHERE priority = ? ORDER BY complaint_date DESC";
        List<Complaint> complaints = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, priority);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                complaints.add(extractComplaintFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return complaints;
    }
    
    /**
     * 获取待处理投诉
     */
    public List<Complaint> getPendingComplaints() {
        String sql = "SELECT * FROM complaints WHERE status IN ('待处理', '处理中') ORDER BY priority DESC, complaint_date ASC";
        List<Complaint> complaints = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                complaints.add(extractComplaintFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return complaints;
    }
    
    /**
     * 获取投诉统计信息
     */
    public int getComplaintCountByStatus(String status) {
        String sql = "SELECT COUNT(*) FROM complaints WHERE status = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, status);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DatabaseUtil.closeConnection(conn, pstmt, rs);
        }
        return 0;
    }
    
    /**
     * 从ResultSet中提取Complaint对象
     */
    private Complaint extractComplaintFromResultSet(ResultSet rs) throws SQLException {
        Complaint complaint = new Complaint();
        complaint.setId(rs.getInt("id"));
        complaint.setCustomerName(rs.getString("customer_name"));
        complaint.setCustomerPhone(rs.getString("customer_phone"));
        complaint.setCustomerEmail(rs.getString("customer_email"));
        complaint.setComplaintType(rs.getString("complaint_type"));
        complaint.setComplaintContent(rs.getString("complaint_content"));
        complaint.setRelatedService(rs.getString("related_service"));
        complaint.setComplaintDate(rs.getTimestamp("complaint_date"));
        complaint.setStatus(rs.getString("status"));
        complaint.setPriority(rs.getString("priority"));
        complaint.setHandler(rs.getString("handler"));
        complaint.setHandleDate(rs.getTimestamp("handle_date"));
        complaint.setHandleResult(rs.getString("handle_result"));
        complaint.setDescription(rs.getString("description"));
        return complaint;
    }
}
