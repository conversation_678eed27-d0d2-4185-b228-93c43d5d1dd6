# 🔧 问题诊断和解决指南

## 🚨 当前问题状态

### 问题1: 主页面无法打开导游管理界面
### 问题2: 所有模块无法查找到数据库数据

---

## 🔍 问题诊断步骤

### 步骤1: 检查Servlet配置
我已经在 `web.xml` 中手动添加了所有Servlet配置，确保它们能被正确识别。

### 步骤2: 修复数据库配置文件
重新创建了干净的 `database.properties` 文件，移除了乱码的中文注释。

### 步骤3: 创建数据库测试工具
添加了专门的数据库测试Servlet，可以直接诊断连接问题。

---

## 🧪 立即测试步骤

### 1. 重启服务器
**重要**: 修改了web.xml和配置文件后，必须重启Tomcat服务器！

### 2. 测试数据库连接
访问: `http://localhost:8080/webtest/database-test`

这个页面会显示：
- ✅ 数据库连接状态
- 📊 数据库信息
- 📋 数据表检查结果
- 🔧 配置信息
- 💡 解决建议

### 3. 测试Servlet映射
访问以下URL测试各个模块：
- 游客管理: `http://localhost:8080/webtest/tourist`
- 饭店管理: `http://localhost:8080/webtest/hotel`
- 旅行社管理: `http://localhost:8080/webtest/agency`
- 导游管理: `http://localhost:8080/webtest/guide`
- 公寓管理: `http://localhost:8080/webtest/apartment`
- 投诉管理: `http://localhost:8080/webtest/complaint`
- 企业档案: `http://localhost:8080/webtest/enterprise`

---

## 🗄️ 数据库配置检查

### 当前配置 (database.properties)
```properties
db.driver=com.mysql.cj.jdbc.Driver
db.url=******************************************************************************************************************************
db.username=root
db.password=root
```

### 如果连接失败，尝试以下配置：

#### 配置1: 密码为123456
```properties
db.password=123456
```

#### 配置2: 空密码
```properties
db.password=
```

#### 配置3: 其他常见密码
```properties
db.password=mysql
db.password=admin
```

---

## 🔧 数据库设置步骤

### 1. 确保MySQL服务运行
```bash
# Windows
net start mysql

# 或者通过服务管理器启动MySQL服务
```

### 2. 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE tourism_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE tourism_system;

-- 检查数据库
SHOW DATABASES;
```

### 3. 导入表结构和数据
```bash
# 在项目根目录执行
mysql -u root -p tourism_system < database/init.sql
```

### 4. 验证表和数据
```sql
-- 检查表
USE tourism_system;
SHOW TABLES;

-- 检查数据
SELECT COUNT(*) FROM tourists;
SELECT COUNT(*) FROM hotels;
SELECT COUNT(*) FROM travel_agencies;
SELECT COUNT(*) FROM guides;
SELECT COUNT(*) FROM apartments;
SELECT COUNT(*) FROM complaints;
SELECT COUNT(*) FROM enterprises;
```

---

## 🚀 解决方案执行清单

### ✅ 已完成的修复
- [x] 修复database.properties文件乱码
- [x] 在web.xml中添加所有Servlet配置
- [x] 创建数据库测试Servlet
- [x] 增强数据库工具类日志输出
- [x] 更新数据库默认密码为"root"

### 🔄 需要您执行的步骤
- [ ] **重启Tomcat服务器** (最重要!)
- [ ] 确保MySQL服务运行
- [ ] 创建tourism_system数据库
- [ ] 导入database/init.sql脚本
- [ ] 访问数据库测试页面验证连接

---

## 🧪 测试验证步骤

### 1. 数据库连接测试
访问: `http://localhost:8080/webtest/database-test`

**期望结果**:
- ✅ 数据库连接成功
- ✅ 所有7个表都存在
- ✅ 配置信息正确显示

### 2. 导游管理测试
访问: `http://localhost:8080/webtest/guide`

**期望结果**:
- ✅ 页面正常加载
- ✅ 显示导游列表（应该有3条记录）
- ✅ 可以点击"添加导游"按钮

### 3. 其他模块测试
依次测试所有模块，确保：
- ✅ 页面正常加载
- ✅ 数据正常显示
- ✅ 添加/编辑/删除功能正常

---

## 🔍 常见错误和解决方案

### 错误1: 404 Not Found
**原因**: Servlet映射问题
**解决**: 重启服务器，确保web.xml配置生效

### 错误2: 500 Internal Server Error
**原因**: 数据库连接失败
**解决**: 检查数据库配置和服务状态

### 错误3: 页面空白或无数据
**原因**: 数据库表不存在或无数据
**解决**: 运行init.sql脚本导入表结构和数据

### 错误4: 中文乱码
**原因**: 字符编码问题
**解决**: 确保数据库字符集为utf8mb4

---

## 📞 调试信息收集

如果问题仍然存在，请收集以下信息：

### 1. 服务器日志
查看Tomcat的catalina.out或localhost.log文件

### 2. 数据库测试结果
访问 `/database-test` 页面的完整输出

### 3. 浏览器控制台
按F12查看浏览器控制台的错误信息

### 4. 网络请求
查看浏览器网络面板的请求状态

---

## 🎯 成功标志

当所有问题解决后，您应该能够：

1. ✅ 从主页点击"导游管理"正常跳转
2. ✅ 导游管理页面显示3条导游记录
3. ✅ 所有模块都能正常显示数据
4. ✅ 添加/编辑/删除功能正常工作
5. ✅ 搜索功能正常工作

---

## 🚀 下一步操作

**立即执行**:
1. **重启Tomcat服务器** 
2. 访问 `http://localhost:8080/webtest/database-test` 测试数据库
3. 访问 `http://localhost:8080/webtest/guide` 测试导游管理
4. 如有问题，查看服务器日志并提供错误信息

**记住**: 修改web.xml后必须重启服务器才能生效！
