@echo off
chcp 65001 >nul
echo ========================================
echo 🗄️ 数据库状态检查
echo ========================================

echo 📋 检查MySQL服务状态...

:: 检查常见的MySQL服务名
set MYSQL_FOUND=0

echo 🔍 检查MySQL服务...
sc query MySQL 2>nul | find "STATE" >nul
if %errorlevel% equ 0 (
    echo ✅ 找到MySQL服务
    sc query MySQL | find "STATE"
    set MYSQL_FOUND=1
)

sc query MySQL80 2>nul | find "STATE" >nul
if %errorlevel% equ 0 (
    echo ✅ 找到MySQL80服务
    sc query MySQL80 | find "STATE"
    set MYSQL_FOUND=1
)

sc query MySQL57 2>nul | find "STATE" >nul
if %errorlevel% equ 0 (
    echo ✅ 找到MySQL57服务
    sc query MySQL57 | find "STATE"
    set MYSQL_FOUND=1
)

if %MYSQL_FOUND% equ 0 (
    echo ❌ 未找到MySQL服务
    echo.
    echo 💡 可能的解决方案:
    echo   1. 安装MySQL数据库
    echo   2. 启动MySQL服务
    echo   3. 检查XAMPP/WAMP是否已启动
    echo.
    goto :end
)

echo.
echo 🔌 测试数据库连接...
echo 📍 尝试连接到: localhost:3306
echo 🗄️ 数据库: tourism_system
echo 👤 用户: root

:: 检查端口3306是否开放
netstat -an | find ":3306" >nul
if %errorlevel% equ 0 (
    echo ✅ 端口3306已开放
) else (
    echo ❌ 端口3306未开放
    echo 💡 请启动MySQL服务
)

echo.
echo 📝 数据库配置文件内容:
echo ----------------------------------------
type src\database.properties 2>nul
if %errorlevel% neq 0 (
    echo ❌ 未找到数据库配置文件
)

echo.
echo ========================================
echo 🚀 启动建议:
echo ========================================
echo 1. 确保MySQL服务已启动
echo 2. 创建tourism_system数据库
echo 3. 导入database\init.sql脚本
echo 4. 运行 run-simple.bat 启动系统
echo 5. 访问 http://localhost:8080/webtest/database-test 测试连接

:end
echo.
pause
