# 快速启动指南

## 🚀 5分钟快速启动

### 第一步：准备环境
确保您已安装：
- ✅ JDK 8+
- ✅ Apache Tomcat 9.0+
- ✅ MySQL 8.0+
- ✅ IntelliJ IDEA 或 Eclipse

### 第二步：下载依赖
下载并放入 `web/WEB-INF/lib/` 目录：
1. **JSTL库**: [jstl-1.2.jar](https://repo1.maven.org/maven2/javax/servlet/jstl/1.2/jstl-1.2.jar)
2. **MySQL驱动**: [mysql-connector-java-8.0.33.jar](https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar)

### 第三步：配置数据库
```sql
-- 1. 创建数据库
CREATE DATABASE tourism_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. 执行初始化脚本
mysql -u root -p tourism_system < database/init.sql
```

### 第四步：修改数据库连接
编辑 `src/com/tourism/util/DatabaseUtil.java`：
```java
private static final String URL = "*************************************************************************************************";
private static final String USERNAME = "root";        // 您的MySQL用户名
private static final String PASSWORD = "123456";      // 您的MySQL密码
```

### 第五步：部署和启动
1. 在IDE中配置Tomcat服务器
2. 将项目部署到Tomcat
3. 启动Tomcat服务器
4. 访问 `http://localhost:8080/webtest/`

## 🧪 功能测试

### 测试JSTL标签
访问: `http://localhost:8080/webtest/test-jstl.jsp`
- 如果看到绿色成功消息，说明JSTL配置正确
- 如果看到红色错误消息，请检查jstl-1.2.jar是否正确放置

### 测试游客管理
1. 访问: `http://localhost:8080/webtest/tourist`
2. 点击"添加游客"测试添加功能
3. 测试编辑、删除、搜索功能

## 📁 项目文件结构

```
webtest/
├── src/com/tourism/          # Java源代码
│   ├── dao/                  # 数据访问层
│   ├── filter/               # 过滤器
│   ├── model/                # 实体类
│   ├── servlet/              # 控制器
│   └── util/                 # 工具类
├── web/                      # Web资源
│   ├── WEB-INF/
│   │   ├── lib/              # JAR依赖库
│   │   └── web.xml           # Web配置
│   ├── index.jsp             # 系统首页
│   ├── list.jsp              # 游客列表页
│   ├── add.jsp               # 添加游客页
│   ├── edit.jsp              # 编辑游客页
│   └── test-jstl.jsp         # JSTL测试页
├── database/
│   └── init.sql              # 数据库初始化脚本
└── 文档文件
```

## 🔧 常见问题解决

### 1. JSTL标签不工作
**症状**: JSP页面显示 `${...}` 原始文本
**解决**: 
- 确保 `jstl-1.2.jar` 在 `web/WEB-INF/lib/` 目录
- 重启Tomcat服务器
- 检查JSP页面顶部是否有正确的taglib声明

### 2. 数据库连接失败
**症状**: 500错误，日志显示数据库连接异常
**解决**:
- 检查MySQL服务是否启动
- 验证数据库连接参数（URL、用户名、密码）
- 确保 `mysql-connector-java-8.0.x.jar` 在lib目录

### 3. 中文乱码
**症状**: 页面或数据库中文显示为乱码
**解决**:
- 确保数据库使用utf8mb4字符集
- 检查Tomcat的server.xml配置
- 验证JSP页面编码设置

### 4. 404错误
**症状**: 访问页面显示404
**解决**:
- 检查Tomcat是否正常启动
- 验证项目部署路径
- 确认URL路径是否正确

## 📊 示例数据

数据库初始化脚本已包含示例数据：
- **3个示例游客**
- **3个示例酒店**
- **3个示例旅行社**
- **3个示例导游**
- **其他模块示例数据**

## 🎯 下一步开发

当前已完成游客管理模块，建议按以下顺序继续开发：

1. **星级饭店管理** - 复制游客管理模式
2. **旅行社管理** - 添加车辆关联功能
3. **导游管理** - 添加资质管理
4. **公寓管理** - 添加预订功能
5. **投诉管理** - 添加处理流程
6. **企业档案管理** - 添加文档管理

## 💡 开发提示

### 复用现有代码
- 参考 `TouristDAO.java` 创建其他DAO类
- 参考 `TouristServlet.java` 创建其他Servlet
- 参考游客管理的JSP页面创建其他页面

### 保持一致性
- 使用相同的UI风格和布局
- 遵循相同的命名规范
- 保持相同的错误处理方式

### 测试建议
- 每完成一个模块都要进行完整测试
- 测试所有CRUD操作
- 验证数据验证和错误处理

## 📞 技术支持

如果遇到问题：
1. 查看Tomcat日志文件
2. 检查浏览器开发者工具
3. 参考项目文档
4. 使用测试页面验证配置
