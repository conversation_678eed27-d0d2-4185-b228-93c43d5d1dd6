@echo off
chcp 65001 >nul
echo ========================================
echo 🚀 旅游管理系统 - 简单启动
echo ========================================

echo 📋 检查环境...
java -version 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java环境
    echo 💡 请确保已安装JDK并配置环境变量
    pause
    exit /b 1
)

echo ✅ Java环境正常

echo.
echo 🛑 停止现有进程...
taskkill /F /IM java.exe 2>nul
timeout /t 1 >nul

echo.
echo 🗂️ 准备项目文件...
if not exist "webapps" mkdir webapps
if not exist "temp" mkdir temp
if not exist "logs" mkdir logs

echo.
echo 🔧 配置服务器...
set JAVA_OPTS=-Xms128m -Xmx256m -Dfile.encoding=UTF-8
set CATALINA_HOME=%CD%
set CATALINA_BASE=%CD%

echo.
echo 🌐 启动Web服务器...
echo 📍 项目位置: %CD%\out\artifacts\webtest_war_exploded
echo 🔗 访问地址: http://localhost:8080/webtest/
echo.

start "旅游管理系统" java -cp "lib/*" ^
    -Dfile.encoding=UTF-8 ^
    -Djava.awt.headless=true ^
    -Xms128m -Xmx256m ^
    -Djava.io.tmpdir=temp ^
    -Duser.timezone=Asia/Shanghai ^
    org.apache.catalina.startup.Bootstrap ^
    -config server.xml

echo ⏳ 服务器启动中，请等待...
timeout /t 5 >nul

echo.
echo ✅ 服务器已启动！
echo.
echo 📖 使用说明:
echo   🏠 系统首页: http://localhost:8080/webtest/
echo   🧪 功能测试: http://localhost:8080/webtest/test-simple.jsp
echo   🗄️ 数据库测试: http://localhost:8080/webtest/database-test
echo.
echo 🔍 如果页面无法访问，请检查:
echo   1. 防火墙设置
echo   2. 端口8080是否被占用
echo   3. 数据库是否已启动
echo.
echo 💡 按任意键打开浏览器...
pause >nul

start http://localhost:8080/webtest/

echo.
echo 🎯 服务器正在后台运行
echo 📝 要停止服务器，请关闭Java进程或重新运行此脚本
echo.
pause
