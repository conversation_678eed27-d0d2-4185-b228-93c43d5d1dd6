# 🔧 问题解决方案

## 🚨 当前问题分析

根据您提供的截图，存在以下问题：

### 1. 404错误
- `/tourist` 路径无法访问
- `/guide` 路径无法访问

### 2. 无数据显示
- 饭店列表显示"暂无饭店信息"

## ✅ 解决方案

### 问题1: 缺少游客管理JSP页面

我已经创建了以下文件：
- `web/tourist-list.jsp` - 游客列表页面
- `web/tourist-add.jsp` - 添加游客页面  
- `web/tourist-edit.jsp` - 编辑游客页面

### 问题2: 数据库连接和数据问题

#### 步骤1: 确保MySQL服务运行
```bash
# Windows
net start mysql
# 或通过服务管理器启动MySQL服务
```

#### 步骤2: 创建数据库和导入数据
```sql
-- 1. 登录MySQL
mysql -u root -p

-- 2. 创建数据库
CREATE DATABASE tourism_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 3. 使用数据库
USE tourism_system;

-- 4. 导入初始化脚本
source database/init.sql;

-- 5. 验证数据
SELECT COUNT(*) FROM tourists;
SELECT COUNT(*) FROM hotels;
SELECT COUNT(*) FROM guides;
```

#### 步骤3: 检查数据库配置
确保 `src/database.properties` 中的密码正确：
```properties
db.password=root
# 或者改为您的MySQL密码
# db.password=123456
# db.password=mysql
```

### 问题3: 文件部署

#### 手动复制文件到部署目录：

1. **复制新创建的JSP页面**：
   ```
   复制 web/tourist-list.jsp 到 out/artifacts/webtest_war_exploded/
   复制 web/tourist-add.jsp 到 out/artifacts/webtest_war_exploded/
   复制 web/tourist-edit.jsp 到 out/artifacts/webtest_war_exploded/
   ```

2. **确保所有JSP页面都在部署目录**：
   ```
   out/artifacts/webtest_war_exploded/
   ├── tourist-list.jsp
   ├── tourist-add.jsp  
   ├── tourist-edit.jsp
   ├── guide-list.jsp
   ├── hotel-list.jsp
   └── ... 其他JSP页面
   ```

3. **复制数据库配置**：
   ```
   复制 src/database.properties 到 out/artifacts/webtest_war_exploded/WEB-INF/classes/
   ```

## 🚀 立即执行步骤

### 1. 数据库设置
```sql
-- 创建数据库
CREATE DATABASE tourism_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE tourism_system;

-- 导入数据（在命令行执行）
mysql -u root -p tourism_system < database/init.sql
```

### 2. 验证数据
```sql
-- 检查表是否创建成功
SHOW TABLES;

-- 检查是否有示例数据
SELECT * FROM tourists LIMIT 5;
SELECT * FROM hotels LIMIT 5;
SELECT * FROM guides LIMIT 5;
```

### 3. 重启Tomcat
- 停止Tomcat服务器
- 重新启动Tomcat
- 访问 `http://localhost:8080/webtest/`

### 4. 测试访问
- 游客管理: `http://localhost:8080/webtest/tourist`
- 导游管理: `http://localhost:8080/webtest/guide`
- 饭店管理: `http://localhost:8080/webtest/hotel`

## 🔍 故障排除

### 如果仍然404错误：
1. 检查 `web.xml` 中的Servlet映射
2. 确保所有Java类已编译到 `WEB-INF/classes` 目录
3. 检查Tomcat日志查看具体错误

### 如果仍然无数据：
1. 确认MySQL服务已启动
2. 检查数据库连接配置
3. 确认已导入 `database/init.sql` 脚本
4. 检查数据库中是否有数据：
   ```sql
   SELECT COUNT(*) FROM hotels;
   ```

### 如果中文乱码：
1. 确保数据库字符集为 utf8mb4
2. 检查JSP页面编码设置
3. 确保Tomcat配置支持UTF-8

## 📋 检查清单

- [ ] MySQL服务已启动
- [ ] tourism_system数据库已创建
- [ ] database/init.sql脚本已导入
- [ ] 数据库中有示例数据
- [ ] 数据库密码配置正确
- [ ] 新的JSP页面已复制到部署目录
- [ ] Tomcat已重启
- [ ] 可以访问系统首页

## 🎯 预期结果

完成以上步骤后，您应该能够：
- ✅ 正常访问 `/tourist` 页面
- ✅ 看到游客列表数据
- ✅ 正常访问 `/guide` 页面  
- ✅ 看到导游列表数据
- ✅ 正常访问 `/hotel` 页面
- ✅ 看到饭店列表数据

## 💡 重要提示

1. **数据库是关键** - 确保MySQL服务运行且数据已导入
2. **文件部署** - 新创建的JSP文件需要复制到部署目录
3. **重启服务器** - 修改配置后必须重启Tomcat
4. **检查日志** - 如有问题，查看Tomcat日志获取详细错误信息

如果按照以上步骤操作后仍有问题，请提供：
- Tomcat启动日志
- 浏览器控制台错误信息
- 数据库连接测试结果
