# 🗄️ 数据库配置指南

## 📋 概述
本指南将帮助您配置MySQL数据库，使旅游管理系统能够正常连接和显示数据。

---

## 🚀 快速开始

### 1. 确保MySQL服务运行
```bash
# Windows
net start mysql

# Linux/Mac
sudo systemctl start mysql
# 或
sudo service mysql start
```

### 2. 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE tourism_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE tourism_system;

-- 退出MySQL
EXIT;
```

### 3. 导入数据表结构
```bash
# 在项目根目录执行
mysql -u root -p tourism_system < database/init.sql
```

### 4. 测试连接
访问: `http://localhost:8080/webtest/database-test.jsp`

---

## ⚙️ 详细配置步骤

### 步骤1: 检查MySQL安装
确保MySQL已正确安装并运行：

```bash
# 检查MySQL版本
mysql --version

# 检查MySQL服务状态
# Windows
sc query mysql
# Linux
systemctl status mysql
```

### 步骤2: 配置数据库连接
编辑 `src/database.properties` 文件：

```properties
# 基本配置
db.driver=com.mysql.cj.jdbc.Driver
db.url=******************************************************************************************************************************
db.username=root
db.password=你的MySQL密码

# 常见密码配置
# db.password=root
# db.password=123456
# db.password=mysql
# db.password=admin
# db.password=（留空表示无密码）
```

### 步骤3: 创建数据库和表
```sql
-- 1. 登录MySQL
mysql -u root -p

-- 2. 创建数据库
CREATE DATABASE IF NOT EXISTS tourism_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 3. 选择数据库
USE tourism_system;

-- 4. 检查数据库是否创建成功
SHOW DATABASES;
```

### 步骤4: 导入表结构
有两种方式导入表结构：

#### 方式1: 命令行导入（推荐）
```bash
# 在项目根目录执行
mysql -u root -p tourism_system < database/init.sql
```

#### 方式2: MySQL客户端导入
```sql
-- 在MySQL客户端中执行
USE tourism_system;
SOURCE /path/to/your/project/database/init.sql;
```

### 步骤5: 验证表结构
```sql
-- 查看所有表
SHOW TABLES;

-- 查看表结构
DESCRIBE tourists;
DESCRIBE hotels;
DESCRIBE travel_agencies;
DESCRIBE guides;
DESCRIBE apartments;
DESCRIBE complaints;
DESCRIBE enterprises;
```

---

## 🔧 常见问题解决

### 问题1: 连接被拒绝
**错误**: `Connection refused` 或 `Could not connect`

**解决方案**:
1. 确保MySQL服务正在运行
2. 检查端口号（默认3306）
3. 检查防火墙设置

```bash
# 检查MySQL是否在运行
netstat -an | grep 3306
# 或
telnet localhost 3306
```

### 问题2: 认证失败
**错误**: `Access denied for user 'root'@'localhost'`

**解决方案**:
1. 检查用户名和密码
2. 重置MySQL root密码

```sql
-- 重置root密码
ALTER USER 'root'@'localhost' IDENTIFIED BY '新密码';
FLUSH PRIVILEGES;
```

### 问题3: 数据库不存在
**错误**: `Unknown database 'tourism_system'`

**解决方案**:
```sql
-- 创建数据库
CREATE DATABASE tourism_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 问题4: 表不存在
**错误**: `Table 'tourism_system.tourists' doesn't exist`

**解决方案**:
```bash
# 重新导入表结构
mysql -u root -p tourism_system < database/init.sql
```

### 问题5: 字符编码问题
**错误**: 中文显示乱码

**解决方案**:
1. 确保数据库字符集为utf8mb4
2. 检查连接URL中的字符编码参数

```sql
-- 检查数据库字符集
SHOW CREATE DATABASE tourism_system;

-- 修改数据库字符集
ALTER DATABASE tourism_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

---

## 📊 数据库结构概览

### 数据表列表
| 表名 | 说明 | 主要字段 |
|------|------|----------|
| tourists | 游客信息 | id, name, id_card, phone, email |
| hotels | 饭店信息 | id, name, star_level, address, phone |
| travel_agencies | 旅行社信息 | id, name, license_number, address |
| guides | 导游信息 | id, name, license_number, languages |
| apartments | 公寓信息 | id, name, room_type, price_per_night |
| complaints | 投诉信息 | id, customer_name, complaint_type, status |
| enterprises | 企业档案 | id, name, type, license_number |

### 关键特性
- ✅ **UTF8MB4编码** - 支持完整的Unicode字符
- ✅ **时间戳字段** - 自动记录创建和更新时间
- ✅ **唯一约束** - 身份证号、营业执照号等唯一性
- ✅ **索引优化** - 主键和常用查询字段索引
- ✅ **默认值** - 合理的字段默认值设置

---

## 🧪 测试数据库连接

### 1. 使用测试页面
访问: `http://localhost:8080/webtest/database-test.jsp`

这个页面会：
- ✅ 测试数据库连接
- ✅ 显示配置信息
- ✅ 检查表结构
- ✅ 提供配置建议

### 2. 使用功能测试
访问: `http://localhost:8080/webtest/test-simple.jsp`

测试各个模块：
- 游客管理: `http://localhost:8080/webtest/tourist`
- 饭店管理: `http://localhost:8080/webtest/hotel`
- 旅行社管理: `http://localhost:8080/webtest/agency`
- 导游管理: `http://localhost:8080/webtest/guide`
- 公寓管理: `http://localhost:8080/webtest/apartment`
- 投诉管理: `http://localhost:8080/webtest/complaint`
- 企业档案: `http://localhost:8080/webtest/enterprise`

---

## 📝 配置文件说明

### database.properties
```properties
# MySQL驱动类
db.driver=com.mysql.cj.jdbc.Driver

# 数据库连接URL
# localhost:3306 - 本地MySQL服务器，端口3306
# tourism_system - 数据库名称
# useSSL=false - 禁用SSL（开发环境）
# serverTimezone=UTC - 设置时区
# characterEncoding=utf8 - 字符编码
# allowPublicKeyRetrieval=true - 允许公钥检索
db.url=******************************************************************************************************************************

# 数据库用户名
db.username=root

# 数据库密码
db.password=123456
```

---

## 🎯 成功标志

当数据库配置成功后，您应该能够：

1. ✅ **连接测试通过** - database-test.jsp显示连接成功
2. ✅ **表结构完整** - 所有7个表都存在
3. ✅ **页面正常显示** - 各模块页面能正常加载
4. ✅ **数据操作正常** - 能够添加、查看、编辑、删除数据
5. ✅ **中文显示正常** - 没有乱码问题

---

## 🚀 下一步

数据库配置完成后：

1. **添加测试数据** - 通过各模块添加一些测试数据
2. **测试所有功能** - 验证CRUD操作是否正常
3. **查看统计报表** - 测试企业档案统计功能
4. **性能优化** - 根据需要调整数据库配置

---

## 💡 小贴士

1. **备份数据** - 定期备份数据库
2. **监控性能** - 关注数据库性能
3. **安全设置** - 生产环境使用强密码
4. **日志查看** - 查看MySQL错误日志排查问题

```bash
# 备份数据库
mysqldump -u root -p tourism_system > backup.sql

# 恢复数据库
mysql -u root -p tourism_system < backup.sql
```

---

**🎉 数据库配置完成后，您的旅游管理系统就可以正常使用了！**
