<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="contextPath" value="${pageContext.request.contextPath}" />
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSTL 标签测试 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="${contextPath}/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="${contextPath}/">返回首页</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-vial me-2"></i>JSTL 标签库测试</h2>
                <p class="text-muted">此页面用于测试JSTL标签库是否正常工作</p>
                
                <!-- 测试核心标签 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">核心标签测试 (c:)</h5>
                    </div>
                    <div class="card-body">
                        <!-- c:set 和 c:out 测试 -->
                        <c:set var="testMessage" value="JSTL 核心标签工作正常！" />
                        <div class="alert alert-success">
                            <strong>c:set 和 c:out 测试:</strong> <c:out value="${testMessage}" />
                        </div>
                        
                        <!-- c:if 测试 -->
                        <c:set var="isWorking" value="true" />
                        <c:if test="${isWorking}">
                            <div class="alert alert-info">
                                <strong>c:if 测试:</strong> 条件判断标签工作正常！
                            </div>
                        </c:if>
                        
                        <!-- c:choose 测试 -->
                        <c:set var="testNumber" value="2" />
                        <c:choose>
                            <c:when test="${testNumber == 1}">
                                <div class="alert alert-warning">数字是1</div>
                            </c:when>
                            <c:when test="${testNumber == 2}">
                                <div class="alert alert-primary">
                                    <strong>c:choose/c:when 测试:</strong> 数字是2，多条件判断工作正常！
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div class="alert alert-secondary">数字是其他值</div>
                            </c:otherwise>
                        </c:choose>
                        
                        <!-- c:forEach 测试 -->
                        <c:set var="fruits" value="苹果,香蕉,橙子,葡萄" />
                        <div class="alert alert-light">
                            <strong>c:forEach 测试:</strong>
                            <ul class="mb-0">
                                <c:forEach var="fruit" items="${fruits.split(',')}" varStatus="status">
                                    <li>第${status.index + 1}个水果: ${fruit}</li>
                                </c:forEach>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- 测试格式化标签 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">格式化标签测试 (fmt:)</h5>
                    </div>
                    <div class="card-body">
                        <!-- 设置当前时间 -->
                        <jsp:useBean id="now" class="java.util.Date" />
                        
                        <!-- fmt:formatDate 测试 -->
                        <div class="alert alert-success">
                            <strong>fmt:formatDate 测试:</strong>
                            <ul class="mb-0">
                                <li>完整日期时间: <fmt:formatDate value="${now}" pattern="yyyy-MM-dd HH:mm:ss" /></li>
                                <li>仅日期: <fmt:formatDate value="${now}" pattern="yyyy-MM-dd" /></li>
                                <li>仅时间: <fmt:formatDate value="${now}" pattern="HH:mm:ss" /></li>
                                <li>中文格式: <fmt:formatDate value="${now}" pattern="yyyy年MM月dd日 HH时mm分" /></li>
                            </ul>
                        </div>
                        
                        <!-- fmt:formatNumber 测试 -->
                        <c:set var="testPrice" value="1234.567" />
                        <div class="alert alert-info">
                            <strong>fmt:formatNumber 测试:</strong>
                            <ul class="mb-0">
                                <li>原始数字: ${testPrice}</li>
                                <li>货币格式: <fmt:formatNumber value="${testPrice}" type="currency" currencySymbol="¥" /></li>
                                <li>百分比格式: <fmt:formatNumber value="0.85" type="percent" /></li>
                                <li>保留2位小数: <fmt:formatNumber value="${testPrice}" pattern="#,##0.00" /></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- 测试EL表达式 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">EL表达式测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-primary">
                            <strong>EL表达式测试:</strong>
                            <ul class="mb-0">
                                <li>上下文路径: ${pageContext.request.contextPath}</li>
                                <li>服务器信息: ${pageContext.servletContext.serverInfo}</li>
                                <li>当前时间戳: ${now.time}</li>
                                <li>数学运算: 10 + 5 = ${10 + 5}</li>
                                <li>字符串连接: ${'Hello' += ' World!'}</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- 测试结果总结 -->
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i>测试结果</h5>
                    </div>
                    <div class="card-body">
                        <c:choose>
                            <c:when test="${not empty testMessage}">
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-thumbs-up me-2"></i>恭喜！JSTL标签库配置成功</h6>
                                    <p class="mb-0">
                                        如果您能看到上面所有的测试内容正常显示，说明JSTL标签库已经正确配置并可以正常使用。
                                        您现在可以在项目中安全地使用JSTL标签了。
                                    </p>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div class="alert alert-danger">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>JSTL配置有问题</h6>
                                    <p class="mb-0">
                                        如果您看到这条消息，说明JSTL标签库配置有问题。请检查是否已经将jstl-1.2.jar文件
                                        放入web/WEB-INF/lib目录，并重启Tomcat服务器。
                                    </p>
                                </div>
                            </c:otherwise>
                        </c:choose>
                        
                        <div class="mt-3">
                            <a href="${contextPath}/" class="btn btn-primary">
                                <i class="fas fa-home me-2"></i>返回首页
                            </a>
                            <a href="${contextPath}/tourist" class="btn btn-success">
                                <i class="fas fa-users me-2"></i>测试游客管理
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
