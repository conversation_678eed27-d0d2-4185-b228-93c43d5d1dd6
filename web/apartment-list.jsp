<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="com.tourism.model.Apartment" %>
<%@ page import="java.text.DecimalFormat" %>
<%
    String contextPath = request.getContextPath();
    List<Apartment> apartments = (List<Apartment>) request.getAttribute("apartments");
    String keyword = (String) request.getAttribute("keyword");
    String roomType = (String) request.getAttribute("roomType");
    String status = (String) request.getAttribute("status");
    String minPrice = (String) request.getAttribute("minPrice");
    String maxPrice = (String) request.getAttribute("maxPrice");
    Boolean availableOnly = (Boolean) request.getAttribute("availableOnly");
    String message = (String) session.getAttribute("message");
    String error = (String) session.getAttribute("error");
    DecimalFormat df = new DecimalFormat("#0.00");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公寓管理 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/">返回首页</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-building me-2"></i>
                        <% if (availableOnly != null && availableOnly) { %>
                            可预订公寓
                        <% } else { %>
                            公寓管理
                        <% } %>
                    </h2>
                    <div>
                        <% if (availableOnly == null || !availableOnly) { %>
                            <a href="<%=contextPath%>/apartment?action=available" class="btn btn-outline-success me-2">
                                <i class="fas fa-check me-2"></i>可预订公寓
                            </a>
                        <% } else { %>
                            <a href="<%=contextPath%>/apartment" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-list me-2"></i>全部公寓
                            </a>
                        <% } %>
                        <a href="<%=contextPath%>/apartment?action=add" class="btn btn-danger">
                            <i class="fas fa-plus me-2"></i>添加公寓
                        </a>
                    </div>
                </div>

                <!-- 搜索框 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="get" action="<%=contextPath%>/apartment">
                            <input type="hidden" name="action" value="search">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <input type="text" class="form-control" name="keyword" 
                                           placeholder="请输入公寓名称..." 
                                           value="<%=keyword != null ? keyword : ""%>">
                                </div>
                                <div class="col-md-2 mb-2">
                                    <select class="form-select" name="roomType">
                                        <option value="">房间类型</option>
                                        <option value="单人间" <%="单人间".equals(roomType) ? "selected" : ""%>>单人间</option>
                                        <option value="双人间" <%="双人间".equals(roomType) ? "selected" : ""%>>双人间</option>
                                        <option value="套房" <%="套房".equals(roomType) ? "selected" : ""%>>套房</option>
                                        <option value="家庭房" <%="家庭房".equals(roomType) ? "selected" : ""%>>家庭房</option>
                                        <option value="豪华套房" <%="豪华套房".equals(roomType) ? "selected" : ""%>>豪华套房</option>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <select class="form-select" name="status">
                                        <option value="">选择状态</option>
                                        <option value="可预订" <%="可预订".equals(status) ? "selected" : ""%>>可预订</option>
                                        <option value="已满房" <%="已满房".equals(status) ? "selected" : ""%>>已满房</option>
                                        <option value="维修中" <%="维修中".equals(status) ? "selected" : ""%>>维修中</option>
                                        <option value="暂停营业" <%="暂停营业".equals(status) ? "selected" : ""%>>暂停营业</option>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <input type="number" class="form-control" name="minPrice" 
                                           placeholder="最低价格" step="0.01" min="0"
                                           value="<%=minPrice != null ? minPrice : ""%>">
                                </div>
                                <div class="col-md-2 mb-2">
                                    <input type="number" class="form-control" name="maxPrice" 
                                           placeholder="最高价格" step="0.01" min="0"
                                           value="<%=maxPrice != null ? maxPrice : ""%>">
                                </div>
                                <div class="col-md-1 mb-2">
                                    <button type="submit" class="btn btn-outline-primary w-100" title="搜索">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <a href="<%=contextPath%>/apartment" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-refresh me-1"></i>重置搜索
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 消息提示 -->
                <% if (message != null) { %>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <%=message%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("message"); %>
                <% } %>
                <% if (error != null) { %>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%=error%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("error"); %>
                <% } %>

                <!-- 公寓列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">公寓列表</h5>
                    </div>
                    <div class="card-body">
                        <% if (apartments == null || apartments.isEmpty()) { %>
                                <div class="text-center py-4">
                                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无公寓信息</p>
                                    <a href="<%=contextPath%>/apartment?action=add" class="btn btn-danger">
                                        添加第一个公寓
                                    </a>
                                </div>
                        <% } else { %>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>ID</th>
                                                <th>公寓名称</th>
                                                <th>房间类型</th>
                                                <th>房间数量</th>
                                                <th>每晚价格</th>
                                                <th>地址</th>
                                                <th>电话</th>
                                                <th>负责人</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <% for (Apartment apartment : apartments) { %>
                                                <tr>
                                                    <td><%=apartment.getId()%></td>
                                                    <td>
                                                        <strong><%=apartment.getName()%></strong>
                                                        <% if (apartment.getDescription() != null && !apartment.getDescription().trim().isEmpty()) { %>
                                                            <br><small class="text-muted"><%=apartment.getDescription()%></small>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-primary"><%=apartment.getRoomType()%></span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info"><%=apartment.getRoomCount()%>间</span>
                                                    </td>
                                                    <td>
                                                        <strong class="text-success">¥<%=df.format(apartment.getPricePerNight())%></strong>
                                                        <small class="text-muted">/晚</small>
                                                    </td>
                                                    <td><%=apartment.getAddress()%></td>
                                                    <td><%=apartment.getPhone()%></td>
                                                    <td><%=apartment.getManager() != null ? apartment.getManager() : ""%></td>
                                                    <td>
                                                        <% if ("可预订".equals(apartment.getStatus())) { %>
                                                            <span class="badge bg-success"><%=apartment.getStatus()%></span>
                                                        <% } else if ("已满房".equals(apartment.getStatus())) { %>
                                                            <span class="badge bg-warning"><%=apartment.getStatus()%></span>
                                                        <% } else if ("维修中".equals(apartment.getStatus())) { %>
                                                            <span class="badge bg-info"><%=apartment.getStatus()%></span>
                                                        <% } else { %>
                                                            <span class="badge bg-secondary"><%=apartment.getStatus()%></span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="<%=contextPath%>/apartment?action=edit&id=<%=apartment.getId()%>" 
                                                               class="btn btn-sm btn-outline-primary" title="编辑">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="<%=contextPath%>/apartment?action=delete&id=<%=apartment.getId()%>" 
                                                               class="btn btn-sm btn-outline-danger" title="删除"
                                                               onclick="return confirm('确定要删除公寓「<%=apartment.getName()%>」吗？此操作不可恢复！')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <% } %>
                                        </tbody>
                                    </table>
                                </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
