<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String contextPath = request.getContextPath();
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>旅游行业管理与运行调度系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
        }
        .feature-card {
            transition: transform 0.3s;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .navbar-brand {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<%=contextPath%>/">首页</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            管理模块
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<%=contextPath%>/tourist">游客管理</a></li>
                            <li><a class="dropdown-item" href="<%=contextPath%>/hotel">星级饭店管理</a></li>
                            <li><a class="dropdown-item" href="<%=contextPath%>/agency">旅行社管理</a></li>
                            <li><a class="dropdown-item" href="<%=contextPath%>/guide">导游管理</a></li>
                            <li><a class="dropdown-item" href="<%=contextPath%>/apartment">公寓管理</a></li>
                            <li><a class="dropdown-item" href="<%=contextPath%>/complaint">投诉管理</a></li>
                            <li><a class="dropdown-item" href="<%=contextPath%>/enterprise">企业档案管理</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主页横幅 -->
    <section class="hero-section text-center">
        <div class="container">
            <h1 class="display-4 mb-4">旅游行业管理与运行调度系统</h1>
            <p class="lead mb-4">专业的旅游行业综合管理平台，提供全方位的业务管理和调度服务</p>
            <a href="#features" class="btn btn-light btn-lg">了解更多</a>
        </div>
    </section>

    <!-- 功能特色 -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="display-5 mb-3">系统功能</h2>
                    <p class="lead text-muted">全面覆盖旅游行业各个环节的管理需求</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-6 col-lg-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-users fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">游客管理</h5>
                            <p class="card-text">管理内部游客及其详细信息，包括个人资料、联系方式等</p>
                            <a href="<%=contextPath%>/tourist" class="btn btn-primary">进入管理</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-hotel fa-3x text-success mb-3"></i>
                            <h5 class="card-title">星级饭店管理</h5>
                            <p class="card-text">管理合作饭店信息，包括星级、房间数量、设施等详细信息</p>
                            <a href="<%=contextPath%>/hotel" class="btn btn-success">进入管理</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-bus fa-3x text-info mb-3"></i>
                            <h5 class="card-title">旅行社及车辆管理</h5>
                            <p class="card-text">管理旅行社信息和旅游车辆调度，确保运输安全高效</p>
                            <a href="<%=contextPath%>/agency" class="btn btn-info">进入管理</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-user-tie fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">导游管理</h5>
                            <p class="card-text">管理导游信息，包括资质认证、语言能力、专业特长等</p>
                            <a href="<%=contextPath%>/guide" class="btn btn-warning">进入管理</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-building fa-3x text-danger mb-3"></i>
                            <h5 class="card-title">公寓信息管理</h5>
                            <p class="card-text">设定和管理各类住宿信息，提供多样化的住宿选择</p>
                            <a href="<%=contextPath%>/apartment" class="btn btn-danger">进入管理</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-comments fa-3x text-secondary mb-3"></i>
                            <h5 class="card-title">投诉管理</h5>
                            <p class="card-text">建立完善的投诉处理机制，及时响应和解决客户问题</p>
                            <a href="<%=contextPath%>/complaint" class="btn btn-secondary">进入管理</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>旅游行业管理系统</h5>
                    <p class="mb-0">专业的旅游行业综合管理平台</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2025 旅游管理系统. 保留所有权利.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
