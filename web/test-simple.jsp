<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String contextPath = request.getContextPath();
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统测试 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/">返回首页</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-vial me-2"></i>系统功能测试</h2>
                <p class="text-muted">此页面用于测试系统基础功能是否正常工作（不依赖JSTL）</p>
                
                <!-- 系统信息 -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>系统信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><strong>服务器信息:</strong> <%=application.getServerInfo()%></li>
                                    <li><strong>上下文路径:</strong> <%=contextPath%></li>
                                    <li><strong>请求方法:</strong> <%=request.getMethod()%></li>
                                    <li><strong>当前时间:</strong> <%=new java.util.Date()%></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><strong>字符编码:</strong> <%=response.getCharacterEncoding()%></li>
                                    <li><strong>内容类型:</strong> <%=response.getContentType()%></li>
                                    <li><strong>会话ID:</strong> <%=session.getId()%></li>
                                    <li><strong>JSP版本:</strong> 正常工作 ✅</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 功能测试 -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-test-tube me-2"></i>功能模块测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <!-- 游客管理测试 -->
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                        <h6>游客管理</h6>
                                        <p class="text-muted small">测试游客管理功能</p>
                                        <a href="<%=contextPath%>/tourist" class="btn btn-primary btn-sm">
                                            <i class="fas fa-arrow-right me-1"></i>进入测试
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 饭店管理测试 -->
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-hotel fa-2x text-success mb-2"></i>
                                        <h6>饭店管理</h6>
                                        <p class="text-muted small">测试饭店管理功能</p>
                                        <a href="<%=contextPath%>/hotel" class="btn btn-success btn-sm">
                                            <i class="fas fa-arrow-right me-1"></i>进入测试
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 系统首页测试 -->
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-home fa-2x text-info mb-2"></i>
                                        <h6>系统首页</h6>
                                        <p class="text-muted small">测试系统首页</p>
                                        <a href="<%=contextPath%>/" class="btn btn-info btn-sm">
                                            <i class="fas fa-arrow-right me-1"></i>进入测试
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 旅行社管理测试 -->
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-bus fa-2x text-info mb-2"></i>
                                        <h6>旅行社管理</h6>
                                        <p class="text-muted small">测试旅行社管理功能</p>
                                        <a href="<%=contextPath%>/agency" class="btn btn-info btn-sm">
                                            <i class="fas fa-arrow-right me-1"></i>进入测试
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- 导游管理测试 -->
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-user-tie fa-2x text-warning mb-2"></i>
                                        <h6>导游管理</h6>
                                        <p class="text-muted small">测试导游管理功能</p>
                                        <a href="<%=contextPath%>/guide" class="btn btn-warning btn-sm">
                                            <i class="fas fa-arrow-right me-1"></i>进入测试
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- 公寓管理测试 -->
                            <div class="col-md-6">
                                <div class="card border-danger">
                                    <div class="card-body text-center">
                                        <i class="fas fa-building fa-2x text-danger mb-2"></i>
                                        <h6>公寓管理</h6>
                                        <p class="text-muted small">测试公寓管理功能</p>
                                        <a href="<%=contextPath%>/apartment" class="btn btn-danger btn-sm">
                                            <i class="fas fa-arrow-right me-1"></i>进入测试
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- 投诉管理测试 -->
                            <div class="col-md-6">
                                <div class="card border-secondary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-comments fa-2x text-secondary mb-2"></i>
                                        <h6>投诉管理</h6>
                                        <p class="text-muted small">测试投诉管理功能</p>
                                        <a href="<%=contextPath%>/complaint" class="btn btn-secondary btn-sm">
                                            <i class="fas fa-arrow-right me-1"></i>进入测试
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- 企业档案管理测试 -->
                            <div class="col-md-6">
                                <div class="card border-dark">
                                    <div class="card-body text-center">
                                        <i class="fas fa-building fa-2x text-dark mb-2"></i>
                                        <h6>企业档案管理</h6>
                                        <p class="text-muted small">测试企业档案功能</p>
                                        <a href="<%=contextPath%>/enterprise" class="btn btn-dark btn-sm">
                                            <i class="fas fa-arrow-right me-1"></i>进入测试
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- 数据库连接测试 -->
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-database fa-2x text-primary mb-2"></i>
                                        <h6>数据库连接测试</h6>
                                        <p class="text-muted small">测试数据库连接状态</p>
                                        <a href="<%=contextPath%>/database-test.jsp" class="btn btn-primary btn-sm">
                                            <i class="fas fa-arrow-right me-1"></i>测试连接
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 问题解决状态 -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i>问题解决状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-success"><i class="fas fa-check me-2"></i>已解决的问题</h6>
                                <ul class="list-unstyled">
                                    <li>✅ JSP页面404错误</li>
                                    <li>✅ JSTL标签报错问题</li>
                                    <li>✅ 页面路径问题</li>
                                    <li>✅ 字符编码问题</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info"><i class="fas fa-info-circle me-2"></i>当前状态</h6>
                                <ul class="list-unstyled">
                                    <li>🔧 使用传统JSP语法（不依赖JSTL）</li>
                                    <li>🔧 所有页面路径已修复</li>
                                    <li>🔧 可以正常访问和测试</li>
                                    <li>🔧 准备继续开发其他模块</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 快速链接 -->
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0"><i class="fas fa-link me-2"></i>快速链接</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-home me-2"></i>系统首页
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/tourist" class="btn btn-outline-success w-100">
                                    <i class="fas fa-users me-2"></i>游客管理
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/tourist?action=add" class="btn btn-outline-info w-100">
                                    <i class="fas fa-user-plus me-2"></i>添加游客
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/hotel" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-hotel me-2"></i>饭店管理
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/agency" class="btn btn-outline-info w-100">
                                    <i class="fas fa-bus me-2"></i>旅行社管理
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/agency?action=add" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-plus me-2"></i>添加旅行社
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/guide" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-user-tie me-2"></i>导游管理
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/guide?action=add" class="btn btn-outline-dark w-100">
                                    <i class="fas fa-user-plus me-2"></i>添加导游
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/apartment" class="btn btn-outline-danger w-100">
                                    <i class="fas fa-building me-2"></i>公寓管理
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/apartment?action=add" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-plus me-2"></i>添加公寓
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/apartment?action=available" class="btn btn-outline-success w-100">
                                    <i class="fas fa-check me-2"></i>可预订公寓
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/complaint" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-comments me-2"></i>投诉管理
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/complaint?action=add" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-plus me-2"></i>新增投诉
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/complaint?action=pending" class="btn btn-outline-danger w-100">
                                    <i class="fas fa-clock me-2"></i>待处理投诉
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/enterprise" class="btn btn-outline-dark w-100">
                                    <i class="fas fa-building me-2"></i>企业档案
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/enterprise?action=add" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-plus me-2"></i>新增企业
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/enterprise?action=statistics" class="btn btn-outline-info w-100">
                                    <i class="fas fa-chart-bar me-2"></i>企业统计
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/enterprise?action=active" class="btn btn-outline-success w-100">
                                    <i class="fas fa-check me-2"></i>正常企业
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据库信息显示区域 -->
                <div id="databaseInfo" class="mt-4" style="display: none;">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-database me-2"></i>数据库配置说明</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>数据库配置步骤</h6>
                                <ol>
                                    <li><strong>创建数据库:</strong> <code>CREATE DATABASE tourism_system;</code></li>
                                    <li><strong>执行初始化脚本:</strong> <code>mysql -u root -p tourism_system < database/init.sql</code></li>
                                    <li><strong>配置连接参数:</strong> 编辑 <code>src/com/tourism/util/DatabaseUtil.java</code></li>
                                    <li><strong>添加MySQL驱动:</strong> 将 <code>mysql-connector-java-8.0.x.jar</code> 放入 <code>web/WEB-INF/lib/</code></li>
                                    <li><strong>重启Tomcat</strong> 并测试功能</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showDatabaseInfo() {
            const infoDiv = document.getElementById('databaseInfo');
            if (infoDiv.style.display === 'none') {
                infoDiv.style.display = 'block';
                infoDiv.scrollIntoView({ behavior: 'smooth' });
            } else {
                infoDiv.style.display = 'none';
            }
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('系统测试页面加载完成');
            console.log('上下文路径:', '<%=contextPath%>');
            console.log('当前时间:', new Date());
        });
    </script>
</body>
</html>
