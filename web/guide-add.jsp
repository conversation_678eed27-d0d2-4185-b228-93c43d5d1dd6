<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String contextPath = request.getContextPath();
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加导游 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/guide">返回导游列表</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-user-tie me-2"></i>添加导游</h4>
                    </div>
                    <div class="card-body">
                        <form method="post" action="<%=contextPath%>/guide" class="needs-validation" novalidate>
                            <input type="hidden" name="action" value="add">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">姓名 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                    <div class="invalid-feedback">请输入导游姓名</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="idCard" class="form-label">身份证号 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="idCard" name="idCard" 
                                           pattern="[0-9]{17}[0-9Xx]" title="请输入正确的身份证号" required>
                                    <div class="invalid-feedback">请输入正确的身份证号</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">电话 <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           pattern="[0-9]{11}" title="请输入11位手机号" required>
                                    <div class="invalid-feedback">请输入正确的手机号</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="licenseNumber" class="form-label">导游证号 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="licenseNumber" name="licenseNumber" required>
                                    <div class="invalid-feedback">请输入导游证号</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="experienceYears" class="form-label">从业年限</label>
                                    <input type="number" class="form-control" id="experienceYears" name="experienceYears" 
                                           min="0" max="50" value="0">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="languages" class="form-label">语言能力</label>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="lang1" value="中文" onchange="updateLanguages()">
                                            <label class="form-check-label" for="lang1">中文</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="lang2" value="英语" onchange="updateLanguages()">
                                            <label class="form-check-label" for="lang2">英语</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="lang3" value="日语" onchange="updateLanguages()">
                                            <label class="form-check-label" for="lang3">日语</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="lang4" value="韩语" onchange="updateLanguages()">
                                            <label class="form-check-label" for="lang4">韩语</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="lang5" value="法语" onchange="updateLanguages()">
                                            <label class="form-check-label" for="lang5">法语</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="lang6" value="德语" onchange="updateLanguages()">
                                            <label class="form-check-label" for="lang6">德语</label>
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" id="languages" name="languages">
                                <small class="form-text text-muted">请选择导游掌握的语言</small>
                            </div>
                            
                            <div class="mb-3">
                                <label for="specialties" class="form-label">专业特长</label>
                                <textarea class="form-control" id="specialties" name="specialties" rows="3" 
                                          placeholder="例如：历史文化、自然风光、美食文化、摄影指导等"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">个人简介</label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="导游的个人介绍、工作经历、服务特色等"></textarea>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="<%=contextPath%>/guide" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回
                                </a>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-2"></i>保存
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 更新语言能力
        function updateLanguages() {
            var checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            var languages = [];
            checkboxes.forEach(function(checkbox) {
                languages.push(checkbox.value);
            });
            document.getElementById('languages').value = languages.join(',');
        }
        
        // 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
                
                // 默认选中中文
                document.getElementById('lang1').checked = true;
                updateLanguages();
            }, false);
        })();
    </script>
</body>
</html>
