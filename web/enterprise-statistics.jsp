<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String contextPath = request.getContextPath();
    Integer activeCount = (Integer) request.getAttribute("activeCount");
    Integer hotelCount = (Integer) request.getAttribute("hotelCount");
    Integer travelAgencyCount = (Integer) request.getAttribute("travelAgencyCount");
    Integer transportCount = (Integer) request.getAttribute("transportCount");
    Integer attractionCount = (Integer) request.getAttribute("attractionCount");
    Integer otherCount = (Integer) request.getAttribute("otherCount");
    
    int totalCount = (hotelCount != null ? hotelCount : 0) + 
                    (travelAgencyCount != null ? travelAgencyCount : 0) + 
                    (transportCount != null ? transportCount : 0) + 
                    (attractionCount != null ? attractionCount : 0) + 
                    (otherCount != null ? otherCount : 0);
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业档案统计 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/enterprise">返回企业档案</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-chart-bar me-2"></i>企业档案统计</h2>
                    <div>
                        <a href="<%=contextPath%>/enterprise" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-list me-2"></i>企业列表
                        </a>
                        <a href="<%=contextPath%>/enterprise?action=add" class="btn btn-dark">
                            <i class="fas fa-plus me-2"></i>新增企业
                        </a>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h4 class="text-success"><%=activeCount != null ? activeCount : 0%></h4>
                                <p class="text-muted mb-0">正常企业</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-hotel fa-2x text-primary mb-2"></i>
                                <h4 class="text-primary"><%=hotelCount != null ? hotelCount : 0%></h4>
                                <p class="text-muted mb-0">酒店企业</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-plane fa-2x text-info mb-2"></i>
                                <h4 class="text-info"><%=travelAgencyCount != null ? travelAgencyCount : 0%></h4>
                                <p class="text-muted mb-0">旅行社</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-dark">
                            <div class="card-body text-center">
                                <i class="fas fa-building fa-2x text-dark mb-2"></i>
                                <h4 class="text-dark"><%=totalCount%></h4>
                                <p class="text-muted mb-0">企业总数</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">企业类型分布</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="typeChart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">企业类型统计</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>企业类型</th>
                                                <th>数量</th>
                                                <th>占比</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><span class="badge bg-primary">酒店</span></td>
                                                <td><%=hotelCount != null ? hotelCount : 0%></td>
                                                <td><%=totalCount > 0 ? String.format("%.1f%%", (hotelCount != null ? hotelCount : 0) * 100.0 / totalCount) : "0%"%></td>
                                                <td>
                                                    <a href="<%=contextPath%>/enterprise?action=search&type=酒店" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> 查看
                                                    </a>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge bg-success">旅行社</span></td>
                                                <td><%=travelAgencyCount != null ? travelAgencyCount : 0%></td>
                                                <td><%=totalCount > 0 ? String.format("%.1f%%", (travelAgencyCount != null ? travelAgencyCount : 0) * 100.0 / totalCount) : "0%"%></td>
                                                <td>
                                                    <a href="<%=contextPath%>/enterprise?action=search&type=旅行社" class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-eye"></i> 查看
                                                    </a>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge bg-warning">交通运输</span></td>
                                                <td><%=transportCount != null ? transportCount : 0%></td>
                                                <td><%=totalCount > 0 ? String.format("%.1f%%", (transportCount != null ? transportCount : 0) * 100.0 / totalCount) : "0%"%></td>
                                                <td>
                                                    <a href="<%=contextPath%>/enterprise?action=search&type=交通运输" class="btn btn-sm btn-outline-warning">
                                                        <i class="fas fa-eye"></i> 查看
                                                    </a>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge bg-info">景区景点</span></td>
                                                <td><%=attractionCount != null ? attractionCount : 0%></td>
                                                <td><%=totalCount > 0 ? String.format("%.1f%%", (attractionCount != null ? attractionCount : 0) * 100.0 / totalCount) : "0%"%></td>
                                                <td>
                                                    <a href="<%=contextPath%>/enterprise?action=search&type=景区景点" class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-eye"></i> 查看
                                                    </a>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge bg-secondary">其他</span></td>
                                                <td><%=otherCount != null ? otherCount : 0%></td>
                                                <td><%=totalCount > 0 ? String.format("%.1f%%", (otherCount != null ? otherCount : 0) * 100.0 / totalCount) : "0%"%></td>
                                                <td>
                                                    <a href="<%=contextPath%>/enterprise?action=search&type=其他" class="btn btn-sm btn-outline-secondary">
                                                        <i class="fas fa-eye"></i> 查看
                                                    </a>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">快速操作</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <a href="<%=contextPath%>/enterprise?action=search&status=正常" class="btn btn-outline-success w-100">
                                            <i class="fas fa-check me-2"></i>正常企业
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="<%=contextPath%>/enterprise?action=search&status=注销" class="btn btn-outline-secondary w-100">
                                            <i class="fas fa-times me-2"></i>注销企业
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="<%=contextPath%>/enterprise?action=search&status=吊销" class="btn btn-outline-danger w-100">
                                            <i class="fas fa-ban me-2"></i>吊销企业
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="<%=contextPath%>/enterprise?action=search&status=停业" class="btn btn-outline-warning w-100">
                                            <i class="fas fa-pause me-2"></i>停业企业
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 企业类型分布饼图
        const ctx = document.getElementById('typeChart').getContext('2d');
        const typeChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['酒店', '旅行社', '交通运输', '景区景点', '其他'],
                datasets: [{
                    data: [
                        <%=hotelCount != null ? hotelCount : 0%>,
                        <%=travelAgencyCount != null ? travelAgencyCount : 0%>,
                        <%=transportCount != null ? transportCount : 0%>,
                        <%=attractionCount != null ? attractionCount : 0%>,
                        <%=otherCount != null ? otherCount : 0%>
                    ],
                    backgroundColor: [
                        '#0d6efd',
                        '#198754',
                        '#ffc107',
                        '#0dcaf0',
                        '#6c757d'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((context.parsed * 100) / total).toFixed(1) : 0;
                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
