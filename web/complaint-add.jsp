<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String contextPath = request.getContextPath();
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增投诉 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/complaint">返回投诉列表</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-comments me-2"></i>新增投诉</h4>
                    </div>
                    <div class="card-body">
                        <form method="post" action="<%=contextPath%>/complaint" class="needs-validation" novalidate>
                            <input type="hidden" name="action" value="add">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="customerName" class="form-label">客户姓名 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="customerName" name="customerName" required>
                                    <div class="invalid-feedback">请输入客户姓名</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="customerPhone" class="form-label">联系电话 <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="customerPhone" name="customerPhone" 
                                           pattern="[0-9]{11}" title="请输入11位手机号" required>
                                    <div class="invalid-feedback">请输入正确的手机号</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="customerEmail" class="form-label">客户邮箱</label>
                                    <input type="email" class="form-control" id="customerEmail" name="customerEmail">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="complaintType" class="form-label">投诉类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="complaintType" name="complaintType" required>
                                        <option value="">请选择投诉类型</option>
                                        <option value="服务质量">服务质量</option>
                                        <option value="价格问题">价格问题</option>
                                        <option value="安全问题">安全问题</option>
                                        <option value="设施问题">设施问题</option>
                                        <option value="行程安排">行程安排</option>
                                        <option value="其他">其他</option>
                                    </select>
                                    <div class="invalid-feedback">请选择投诉类型</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="relatedService" class="form-label">相关服务</label>
                                    <input type="text" class="form-control" id="relatedService" name="relatedService" 
                                           placeholder="例如：酒店预订、导游服务、交通安排等">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="priority" class="form-label">优先级</label>
                                    <select class="form-select" id="priority" name="priority">
                                        <option value="普通" selected>普通</option>
                                        <option value="重要">重要</option>
                                        <option value="紧急">紧急</option>
                                        <option value="低">低</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="complaintContent" class="form-label">投诉内容 <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="complaintContent" name="complaintContent" rows="5" 
                                          placeholder="请详细描述您遇到的问题..." required></textarea>
                                <div class="invalid-feedback">请输入投诉内容</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">补充说明</label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="其他需要说明的情况..."></textarea>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>温馨提示：</strong>
                                <ul class="mb-0 mt-2">
                                    <li>我们会在24小时内响应您的投诉</li>
                                    <li>请提供准确的联系方式，以便我们及时与您沟通</li>
                                    <li>投诉提交后，您可以通过系统查询处理进度</li>
                                </ul>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="<%=contextPath%>/complaint" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回
                                </a>
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-paper-plane me-2"></i>提交投诉
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // 投诉类型变化时的提示
        document.getElementById('complaintType').addEventListener('change', function() {
            var type = this.value;
            var relatedService = document.getElementById('relatedService');
            
            switch(type) {
                case '服务质量':
                    relatedService.placeholder = '例如：导游服务、客服态度、服务效率等';
                    break;
                case '价格问题':
                    relatedService.placeholder = '例如：价格不符、额外收费、退款问题等';
                    break;
                case '安全问题':
                    relatedService.placeholder = '例如：交通安全、住宿安全、景点安全等';
                    break;
                case '设施问题':
                    relatedService.placeholder = '例如：酒店设施、交通工具、景点设施等';
                    break;
                case '行程安排':
                    relatedService.placeholder = '例如：行程变更、时间安排、景点安排等';
                    break;
                default:
                    relatedService.placeholder = '例如：酒店预订、导游服务、交通安排等';
            }
        });
    </script>
</body>
</html>
