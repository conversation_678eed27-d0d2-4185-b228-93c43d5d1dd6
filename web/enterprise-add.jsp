<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String contextPath = request.getContextPath();
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增企业档案 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/enterprise">返回企业档案</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-building me-2"></i>新增企业档案</h4>
                    </div>
                    <div class="card-body">
                        <form method="post" action="<%=contextPath%>/enterprise" class="needs-validation" novalidate>
                            <input type="hidden" name="action" value="add">
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="name" class="form-label">企业名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                    <div class="invalid-feedback">请输入企业名称</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="type" class="form-label">企业类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="type" name="type" required>
                                        <option value="">请选择企业类型</option>
                                        <option value="酒店">酒店</option>
                                        <option value="旅行社">旅行社</option>
                                        <option value="交通运输">交通运输</option>
                                        <option value="景区景点">景区景点</option>
                                        <option value="餐饮服务">餐饮服务</option>
                                        <option value="其他">其他</option>
                                    </select>
                                    <div class="invalid-feedback">请选择企业类型</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="licenseNumber" class="form-label">营业执照号 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="licenseNumber" name="licenseNumber" 
                                           pattern="[0-9A-Z]{18}" title="请输入18位营业执照号" required>
                                    <div class="invalid-feedback">请输入正确的营业执照号</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="legalPerson" class="form-label">法人代表 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="legalPerson" name="legalPerson" required>
                                    <div class="invalid-feedback">请输入法人代表姓名</div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">企业地址 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="address" name="address" required>
                                <div class="invalid-feedback">请输入企业地址</div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">联系电话 <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           pattern="[0-9\-]{7,20}" title="请输入正确的电话号码" required>
                                    <div class="invalid-feedback">请输入正确的联系电话</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">企业邮箱</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="registrationDate" class="form-label">注册日期</label>
                                    <input type="date" class="form-control" id="registrationDate" name="registrationDate">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">企业状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="正常" selected>正常</option>
                                        <option value="注销">注销</option>
                                        <option value="吊销">吊销</option>
                                        <option value="停业">停业</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="businessScope" class="form-label">经营范围</label>
                                <textarea class="form-control" id="businessScope" name="businessScope" rows="3" 
                                          placeholder="请描述企业的主要经营范围..."></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">企业描述</label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="其他需要说明的企业信息..."></textarea>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>填写提示：</strong>
                                <ul class="mb-0 mt-2">
                                    <li>营业执照号必须唯一，系统会自动检查重复</li>
                                    <li>企业类型将影响统计报表的分类显示</li>
                                    <li>联系电话支持固话和手机号码格式</li>
                                    <li>注册日期可以为空，系统不会强制要求</li>
                                </ul>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="<%=contextPath%>/enterprise" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回
                                </a>
                                <button type="submit" class="btn btn-dark">
                                    <i class="fas fa-save me-2"></i>保存企业档案
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // 企业类型变化时的提示
        document.getElementById('type').addEventListener('change', function() {
            var type = this.value;
            var businessScope = document.getElementById('businessScope');
            
            switch(type) {
                case '酒店':
                    businessScope.placeholder = '例如：住宿服务、餐饮服务、会议服务、娱乐服务等';
                    break;
                case '旅行社':
                    businessScope.placeholder = '例如：国内旅游、出境旅游、入境旅游、旅游咨询等';
                    break;
                case '交通运输':
                    businessScope.placeholder = '例如：客运服务、货运服务、租车服务、机场接送等';
                    break;
                case '景区景点':
                    businessScope.placeholder = '例如：景区管理、门票销售、导游服务、纪念品销售等';
                    break;
                case '餐饮服务':
                    businessScope.placeholder = '例如：中餐、西餐、快餐、特色小吃、饮品服务等';
                    break;
                default:
                    businessScope.placeholder = '请描述企业的主要经营范围...';
            }
        });
        
        // 营业执照号格式化
        document.getElementById('licenseNumber').addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    </script>
</body>
</html>
