<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>旅游管理系统 - 演示版</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 0.8em;
        }
        .demo-section {
            background: #f8f9fa;
            padding: 60px 0;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">功能模块</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#demo">系统演示</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#setup">部署说明</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">🏖️ 旅游管理系统</h1>
            <p class="lead mb-4">完整的旅游行业管理解决方案</p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>当前状态:</strong> 演示模式 - 需要配置Tomcat和MySQL才能使用完整功能
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 功能模块 -->
    <section id="features" class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">🎯 核心功能模块</h2>
            <div class="row g-4">
                <!-- 游客管理 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card feature-card h-100 position-relative">
                        <span class="badge bg-success status-badge">✅ 完成</span>
                        <div class="card-body text-center p-4">
                            <i class="fas fa-users fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">游客管理</h5>
                            <p class="card-text">管理游客信息、身份验证、联系方式等基础数据</p>
                            <ul class="list-unstyled text-start">
                                <li><i class="fas fa-check text-success me-2"></i>游客信息录入</li>
                                <li><i class="fas fa-check text-success me-2"></i>身份证验证</li>
                                <li><i class="fas fa-check text-success me-2"></i>联系方式管理</li>
                                <li><i class="fas fa-check text-success me-2"></i>搜索和筛选</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 饭店管理 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card feature-card h-100 position-relative">
                        <span class="badge bg-success status-badge">✅ 完成</span>
                        <div class="card-body text-center p-4">
                            <i class="fas fa-hotel fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">星级饭店管理</h5>
                            <p class="card-text">管理星级饭店信息、房间数量、设施配置等</p>
                            <ul class="list-unstyled text-start">
                                <li><i class="fas fa-check text-success me-2"></i>饭店基本信息</li>
                                <li><i class="fas fa-check text-success me-2"></i>星级评定管理</li>
                                <li><i class="fas fa-check text-success me-2"></i>房间数量统计</li>
                                <li><i class="fas fa-check text-success me-2"></i>设施配置管理</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 旅行社管理 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card feature-card h-100 position-relative">
                        <span class="badge bg-success status-badge">✅ 完成</span>
                        <div class="card-body text-center p-4">
                            <i class="fas fa-bus fa-3x text-info mb-3"></i>
                            <h5 class="card-title">旅行社管理</h5>
                            <p class="card-text">管理旅行社资质、营业执照、经营范围等</p>
                            <ul class="list-unstyled text-start">
                                <li><i class="fas fa-check text-success me-2"></i>旅行社资质管理</li>
                                <li><i class="fas fa-check text-success me-2"></i>营业执照验证</li>
                                <li><i class="fas fa-check text-success me-2"></i>经营范围管理</li>
                                <li><i class="fas fa-check text-success me-2"></i>状态跟踪</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 导游管理 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card feature-card h-100 position-relative">
                        <span class="badge bg-success status-badge">✅ 完成</span>
                        <div class="card-body text-center p-4">
                            <i class="fas fa-user-tie fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">导游管理</h5>
                            <p class="card-text">管理导游信息、资质认证、语言能力等</p>
                            <ul class="list-unstyled text-start">
                                <li><i class="fas fa-check text-success me-2"></i>导游资质认证</li>
                                <li><i class="fas fa-check text-success me-2"></i>语言能力管理</li>
                                <li><i class="fas fa-check text-success me-2"></i>专业特长记录</li>
                                <li><i class="fas fa-check text-success me-2"></i>从业经验统计</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 公寓管理 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card feature-card h-100 position-relative">
                        <span class="badge bg-success status-badge">✅ 完成</span>
                        <div class="card-body text-center p-4">
                            <i class="fas fa-building fa-3x text-success mb-3"></i>
                            <h5 class="card-title">公寓管理</h5>
                            <p class="card-text">管理公寓信息、房型配置、价格管理等</p>
                            <ul class="list-unstyled text-start">
                                <li><i class="fas fa-check text-success me-2"></i>公寓基本信息</li>
                                <li><i class="fas fa-check text-success me-2"></i>房型配置管理</li>
                                <li><i class="fas fa-check text-success me-2"></i>价格管理系统</li>
                                <li><i class="fas fa-check text-success me-2"></i>预订状态跟踪</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 投诉管理 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card feature-card h-100 position-relative">
                        <span class="badge bg-success status-badge">✅ 完成</span>
                        <div class="card-body text-center p-4">
                            <i class="fas fa-comments fa-3x text-secondary mb-3"></i>
                            <h5 class="card-title">投诉管理</h5>
                            <p class="card-text">处理客户投诉、跟踪处理进度、质量管控</p>
                            <ul class="list-unstyled text-start">
                                <li><i class="fas fa-check text-success me-2"></i>投诉信息录入</li>
                                <li><i class="fas fa-check text-success me-2"></i>处理进度跟踪</li>
                                <li><i class="fas fa-check text-success me-2"></i>优先级管理</li>
                                <li><i class="fas fa-check text-success me-2"></i>处理结果反馈</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 企业档案管理 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card feature-card h-100 position-relative">
                        <span class="badge bg-success status-badge">✅ 完成</span>
                        <div class="card-body text-center p-4">
                            <i class="fas fa-building fa-3x text-dark mb-3"></i>
                            <h5 class="card-title">企业档案管理</h5>
                            <p class="card-text">管理旅游企业档案、统计分析、可视化报表</p>
                            <ul class="list-unstyled text-start">
                                <li><i class="fas fa-check text-success me-2"></i>企业档案管理</li>
                                <li><i class="fas fa-check text-success me-2"></i>类型分类统计</li>
                                <li><i class="fas fa-check text-success me-2"></i>可视化图表</li>
                                <li><i class="fas fa-check text-success me-2"></i>状态监控</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 系统演示 -->
    <section id="demo" class="demo-section">
        <div class="container">
            <h2 class="text-center mb-5">🖥️ 系统演示</h2>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">📋 项目文件结构</h5>
                        </div>
                        <div class="card-body">
                            <pre class="bg-light p-3 rounded"><code>旅游管理系统/
├── 📁 src/com/tourism/
│   ├── 📁 dao/           # 数据访问层 (7个DAO类)
│   ├── 📁 model/         # 实体模型层 (7个实体类)
│   ├── 📁 servlet/       # 控制器层 (7个Servlet)
│   ├── 📁 filter/        # 过滤器
│   └── 📁 util/          # 工具类
├── 📁 web/               # Web页面 (29个JSP页面)
│   ├── 📁 WEB-INF/       # Web配置
│   ├── 🏠 index.jsp      # 系统首页
│   ├── 🧪 test-simple.jsp # 功能测试页
│   └── 📄 各模块页面...
├── 📁 database/          # 数据库脚本
│   └── 📄 init.sql       # 初始化脚本
└── 📁 lib/               # 依赖库</code></pre>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">🎯 技术特色</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>MVC架构设计</li>
                                <li><i class="fas fa-check text-success me-2"></i>Bootstrap 5响应式界面</li>
                                <li><i class="fas fa-check text-success me-2"></i>Chart.js数据可视化</li>
                                <li><i class="fas fa-check text-success me-2"></i>完整的CRUD操作</li>
                                <li><i class="fas fa-check text-success me-2"></i>高级搜索筛选</li>
                                <li><i class="fas fa-check text-success me-2"></i>数据验证机制</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">📊 项目统计</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-code text-primary me-2"></i>Java类文件: 22个</li>
                                <li><i class="fas fa-file-code text-warning me-2"></i>JSP页面: 29个</li>
                                <li><i class="fas fa-database text-info me-2"></i>数据库表: 7个</li>
                                <li><i class="fas fa-lines-leaning text-success me-2"></i>代码行数: ~8000行</li>
                                <li><i class="fas fa-clock text-secondary me-2"></i>开发时间: 27小时</li>
                                <li><i class="fas fa-percentage text-danger me-2"></i>完成度: 100%</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 部署说明 -->
    <section id="setup" class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">🚀 部署说明</h2>
            <div class="row">
                <div class="col-lg-10 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">📋 环境要求和部署步骤</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>🔧 环境要求</h6>
                                    <ul>
                                        <li>Java JDK 8+</li>
                                        <li>Apache Tomcat 8+</li>
                                        <li>MySQL 5.7+</li>
                                        <li>现代浏览器</li>
                                    </ul>

                                    <h6 class="mt-4">📦 部署步骤</h6>
                                    <ol>
                                        <li>启动MySQL服务</li>
                                        <li>创建tourism_system数据库</li>
                                        <li>导入database/init.sql脚本</li>
                                        <li>配置数据库连接</li>
                                        <li>部署到Tomcat</li>
                                        <li>访问系统首页</li>
                                    </ol>
                                </div>
                                <div class="col-md-6">
                                    <h6>🗄️ 数据库配置</h6>
                                    <pre class="bg-light p-2 small"><code># database.properties
db.url=******************************************
db.username=root
db.password=your_password</code></pre>

                                    <h6 class="mt-3">🌐 访问地址</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>系统首页:</strong><br>
                                            <code>http://localhost:8080/webtest/</code></li>
                                        <li><strong>功能测试:</strong><br>
                                            <code>http://localhost:8080/webtest/test-simple.jsp</code></li>
                                        <li><strong>数据库测试:</strong><br>
                                            <code>http://localhost:8080/webtest/database-test</code></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">🏖️ 旅游管理系统 - 完整的企业级解决方案</p>
            <p class="mb-0 small text-muted">基于Java + JSP + MySQL + Bootstrap 5技术栈开发</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
