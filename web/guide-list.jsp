<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="com.tourism.model.Guide" %>
<%
    String contextPath = request.getContextPath();
    List<Guide> guides = (List<Guide>) request.getAttribute("guides");
    String keyword = (String) request.getAttribute("keyword");
    String status = (String) request.getAttribute("status");
    String language = (String) request.getAttribute("language");
    String message = (String) session.getAttribute("message");
    String error = (String) session.getAttribute("error");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导游管理 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/">返回首页</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-user-tie me-2"></i>导游管理</h2>
                    <a href="<%=contextPath%>/guide?action=add" class="btn btn-warning">
                        <i class="fas fa-plus me-2"></i>添加导游
                    </a>
                </div>

                <!-- 搜索框 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="get" action="<%=contextPath%>/guide">
                            <input type="hidden" name="action" value="search">
                            <div class="row">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" name="keyword" 
                                           placeholder="请输入导游姓名..." 
                                           value="<%=keyword != null ? keyword : ""%>">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" name="status">
                                        <option value="">选择状态</option>
                                        <option value="在职" <%="在职".equals(status) ? "selected" : ""%>>在职</option>
                                        <option value="休假" <%="休假".equals(status) ? "selected" : ""%>>休假</option>
                                        <option value="离职" <%="离职".equals(status) ? "selected" : ""%>>离职</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" name="language">
                                        <option value="">选择语言</option>
                                        <option value="英语" <%="英语".equals(language) ? "selected" : ""%>>英语</option>
                                        <option value="日语" <%="日语".equals(language) ? "selected" : ""%>>日语</option>
                                        <option value="韩语" <%="韩语".equals(language) ? "selected" : ""%>>韩语</option>
                                        <option value="法语" <%="法语".equals(language) ? "selected" : ""%>>法语</option>
                                        <option value="德语" <%="德语".equals(language) ? "selected" : ""%>>德语</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-search me-2"></i>搜索
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <a href="<%=contextPath%>/guide" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-refresh me-2"></i>重置
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 消息提示 -->
                <% if (message != null) { %>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <%=message%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("message"); %>
                <% } %>
                <% if (error != null) { %>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%=error%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("error"); %>
                <% } %>

                <!-- 导游列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">导游列表</h5>
                    </div>
                    <div class="card-body">
                        <% if (guides == null || guides.isEmpty()) { %>
                                <div class="text-center py-4">
                                    <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无导游信息</p>
                                    <a href="<%=contextPath%>/guide?action=add" class="btn btn-warning">
                                        添加第一个导游
                                    </a>
                                </div>
                        <% } else { %>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>ID</th>
                                                <th>姓名</th>
                                                <th>导游证号</th>
                                                <th>电话</th>
                                                <th>语言能力</th>
                                                <th>专业特长</th>
                                                <th>从业年限</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <% for (Guide guide : guides) { %>
                                                <tr>
                                                    <td><%=guide.getId()%></td>
                                                    <td>
                                                        <strong><%=guide.getName()%></strong>
                                                        <% if (guide.getDescription() != null && !guide.getDescription().trim().isEmpty()) { %>
                                                            <br><small class="text-muted"><%=guide.getDescription()%></small>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <code><%=guide.getLicenseNumber()%></code>
                                                    </td>
                                                    <td><%=guide.getPhone()%></td>
                                                    <td>
                                                        <% if (guide.getLanguages() != null && !guide.getLanguages().trim().isEmpty()) { %>
                                                            <% String[] langs = guide.getLanguages().split(","); %>
                                                            <% for (String lang : langs) { %>
                                                                <span class="badge bg-info me-1"><%=lang.trim()%></span>
                                                            <% } %>
                                                        <% } else { %>
                                                            <span class="text-muted">-</span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <% if (guide.getSpecialties() != null && !guide.getSpecialties().trim().isEmpty()) { %>
                                                            <small><%=guide.getSpecialties()%></small>
                                                        <% } else { %>
                                                            <span class="text-muted">-</span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary"><%=guide.getExperienceYears()%>年</span>
                                                    </td>
                                                    <td>
                                                        <% if ("在职".equals(guide.getStatus())) { %>
                                                            <span class="badge bg-success"><%=guide.getStatus()%></span>
                                                        <% } else if ("休假".equals(guide.getStatus())) { %>
                                                            <span class="badge bg-warning"><%=guide.getStatus()%></span>
                                                        <% } else { %>
                                                            <span class="badge bg-secondary"><%=guide.getStatus()%></span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="<%=contextPath%>/guide?action=edit&id=<%=guide.getId()%>" 
                                                               class="btn btn-sm btn-outline-primary" title="编辑">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="<%=contextPath%>/guide?action=delete&id=<%=guide.getId()%>" 
                                                               class="btn btn-sm btn-outline-danger" title="删除"
                                                               onclick="return confirm('确定要删除导游「<%=guide.getName()%>」吗？此操作不可恢复！')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <% } %>
                                        </tbody>
                                    </table>
                                </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
