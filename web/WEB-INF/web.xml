<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
         version="4.0">

    <display-name>旅游行业管理与运行调度系统</display-name>
    <description>Tourism Industry Management and Operation Scheduling System</description>

    <!-- 默认首页 -->
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

    <!-- 字符编码过滤器 -->
    <filter>
        <filter-name>CharacterEncodingFilter</filter-name>
        <filter-class>com.tourism.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>CharacterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- 错误页面配置 -->
    <error-page>
        <error-code>404</error-code>
        <location>/WEB-INF/views/error/404.jsp</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/WEB-INF/views/error/500.jsp</location>
    </error-page>

    <!-- Servlet配置 -->
    <!-- 游客管理Servlet -->
    <servlet>
        <servlet-name>TouristServlet</servlet-name>
        <servlet-class>com.tourism.servlet.TouristServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TouristServlet</servlet-name>
        <url-pattern>/tourist</url-pattern>
    </servlet-mapping>

    <!-- 饭店管理Servlet -->
    <servlet>
        <servlet-name>HotelServlet</servlet-name>
        <servlet-class>com.tourism.servlet.HotelServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>HotelServlet</servlet-name>
        <url-pattern>/hotel</url-pattern>
    </servlet-mapping>

    <!-- 旅行社管理Servlet -->
    <servlet>
        <servlet-name>TravelAgencyServlet</servlet-name>
        <servlet-class>com.tourism.servlet.TravelAgencyServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TravelAgencyServlet</servlet-name>
        <url-pattern>/agency</url-pattern>
    </servlet-mapping>

    <!-- 导游管理Servlet -->
    <servlet>
        <servlet-name>GuideServlet</servlet-name>
        <servlet-class>com.tourism.servlet.GuideServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>GuideServlet</servlet-name>
        <url-pattern>/guide</url-pattern>
    </servlet-mapping>

    <!-- 公寓管理Servlet -->
    <servlet>
        <servlet-name>ApartmentServlet</servlet-name>
        <servlet-class>com.tourism.servlet.ApartmentServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ApartmentServlet</servlet-name>
        <url-pattern>/apartment</url-pattern>
    </servlet-mapping>

    <!-- 投诉管理Servlet -->
    <servlet>
        <servlet-name>ComplaintServlet</servlet-name>
        <servlet-class>com.tourism.servlet.ComplaintServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ComplaintServlet</servlet-name>
        <url-pattern>/complaint</url-pattern>
    </servlet-mapping>

    <!-- 企业档案管理Servlet -->
    <servlet>
        <servlet-name>EnterpriseServlet</servlet-name>
        <servlet-class>com.tourism.servlet.EnterpriseServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>EnterpriseServlet</servlet-name>
        <url-pattern>/enterprise</url-pattern>
    </servlet-mapping>



    <!-- 会话超时设置（30分钟） -->
    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>

</web-app>