<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加游客 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-container { background: white; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .required { color: red; }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="${pageContext.request.contextPath}/tourist">
                    <i class="fas fa-arrow-left me-1"></i>返回列表
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- 页面标题 -->
                <div class="mb-4">
                    <h2><i class="fas fa-user-plus text-primary me-2"></i>添加游客</h2>
                    <p class="text-muted">请填写游客的基本信息</p>
                </div>

                <!-- 消息提示 -->
                <%
                    String error = (String) session.getAttribute("error");
                    if (error != null) {
                %>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <%
                        session.removeAttribute("error");
                    }
                %>

                <!-- 添加表单 -->
                <div class="form-container p-4">
                    <form method="post" action="${pageContext.request.contextPath}/tourist">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="row g-3">
                            <!-- 基本信息 -->
                            <div class="col-12">
                                <h5 class="border-bottom pb-2 mb-3">
                                    <i class="fas fa-user text-primary me-2"></i>基本信息
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="name" class="form-label">
                                    姓名 <span class="required">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="idCard" class="form-label">
                                    身份证号 <span class="required">*</span>
                                </label>
                                <input type="text" class="form-control" id="idCard" name="idCard" 
                                       pattern="[0-9X]{18}" maxlength="18" required>
                                <div class="form-text">请输入18位身份证号码</div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="phone" class="form-label">
                                    联系电话 <span class="required">*</span>
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       pattern="[0-9]{11}" maxlength="11" required>
                                <div class="form-text">请输入11位手机号码</div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="email" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            
                            <!-- 客户信息 -->
                            <div class="col-12 mt-4">
                                <h5 class="border-bottom pb-2 mb-3">
                                    <i class="fas fa-tags text-primary me-2"></i>客户信息
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="customerType" class="form-label">
                                    客户类型 <span class="required">*</span>
                                </label>
                                <select class="form-select" id="customerType" name="customerType" required>
                                    <option value="">请选择客户类型</option>
                                    <option value="VIP客户">VIP客户</option>
                                    <option value="常客">常客</option>
                                    <option value="新客户">新客户</option>
                                    <option value="商务客户">商务客户</option>
                                    <option value="家庭客户">家庭客户</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="address" class="form-label">地址</label>
                                <input type="text" class="form-control" id="address" name="address">
                            </div>
                            
                            <div class="col-12">
                                <label for="preferences" class="form-label">旅游偏好</label>
                                <textarea class="form-control" id="preferences" name="preferences" rows="3"
                                          placeholder="请描述游客的旅游偏好，如：喜欢海滨度假、文化古迹、美食体验等"></textarea>
                            </div>
                            
                            <div class="col-12">
                                <label for="notes" class="form-label">备注</label>
                                <textarea class="form-control" id="notes" name="notes" rows="2"
                                          placeholder="其他需要说明的信息"></textarea>
                            </div>
                        </div>
                        
                        <!-- 按钮组 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="${pageContext.request.contextPath}/tourist" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>取消
                                    </a>
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo me-1"></i>重置
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>保存
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 身份证号验证
        document.getElementById('idCard').addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase();
            e.target.value = value;
            
            if (value.length === 18) {
                // 简单的身份证号校验
                let pattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
                if (!pattern.test(value)) {
                    e.target.setCustomValidity('请输入正确的身份证号码');
                } else {
                    e.target.setCustomValidity('');
                }
            }
        });
        
        // 手机号验证
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value;
            if (value.length === 11) {
                let pattern = /^1[3-9]\d{9}$/;
                if (!pattern.test(value)) {
                    e.target.setCustomValidity('请输入正确的手机号码');
                } else {
                    e.target.setCustomValidity('');
                }
            }
        });
    </script>
</body>
</html>
