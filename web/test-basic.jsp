<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="contextPath" value="${pageContext.request.contextPath}" />
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础功能测试 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="${contextPath}/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="${contextPath}/">返回首页</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-vial me-2"></i>基础功能测试</h2>
                <p class="text-muted">此页面用于测试系统基础功能是否正常工作</p>
                
                <!-- 系统信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">系统信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><strong>服务器信息:</strong> ${pageContext.servletContext.serverInfo}</li>
                                    <li><strong>Servlet版本:</strong> ${pageContext.servletContext.majorVersion}.${pageContext.servletContext.minorVersion}</li>
                                    <li><strong>上下文路径:</strong> ${pageContext.request.contextPath}</li>
                                    <li><strong>请求方法:</strong> ${pageContext.request.method}</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><strong>当前时间:</strong> 
                                        <jsp:useBean id="now" class="java.util.Date" />
                                        ${now}
                                    </li>
                                    <li><strong>字符编码:</strong> ${pageContext.response.characterEncoding}</li>
                                    <li><strong>内容类型:</strong> ${pageContext.response.contentType}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 功能测试 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">功能模块测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <!-- 游客管理测试 -->
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                        <h6>游客管理</h6>
                                        <p class="text-muted small">测试游客管理功能</p>
                                        <a href="${contextPath}/tourist" class="btn btn-primary btn-sm">
                                            <i class="fas fa-test-tube me-1"></i>测试
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 饭店管理测试 -->
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-hotel fa-2x text-success mb-2"></i>
                                        <h6>饭店管理</h6>
                                        <p class="text-muted small">测试饭店管理功能</p>
                                        <a href="${contextPath}/hotel" class="btn btn-success btn-sm">
                                            <i class="fas fa-test-tube me-1"></i>测试
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- JSTL测试 -->
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-code fa-2x text-info mb-2"></i>
                                        <h6>JSTL标签</h6>
                                        <p class="text-muted small">测试JSTL标签库</p>
                                        <a href="${contextPath}/test-jstl.jsp" class="btn btn-info btn-sm">
                                            <i class="fas fa-test-tube me-1"></i>测试
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 数据库连接测试 -->
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-database fa-2x text-warning mb-2"></i>
                                        <h6>数据库连接</h6>
                                        <p class="text-muted small">测试数据库连接</p>
                                        <button class="btn btn-warning btn-sm" onclick="testDatabase()">
                                            <i class="fas fa-test-tube me-1"></i>测试
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 快速链接 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">快速链接</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="${contextPath}/" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-home me-2"></i>系统首页
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="${contextPath}/tourist?action=add" class="btn btn-outline-success w-100">
                                    <i class="fas fa-user-plus me-2"></i>添加游客
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="${contextPath}/hotel?action=add" class="btn btn-outline-info w-100">
                                    <i class="fas fa-hotel me-2"></i>添加饭店
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="${contextPath}/test-jstl.jsp" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-code me-2"></i>JSTL测试
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 测试结果显示区域 -->
                <div id="testResult" class="mt-4" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">测试结果</h5>
                        </div>
                        <div class="card-body">
                            <div id="resultContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testDatabase() {
            const resultDiv = document.getElementById('testResult');
            const contentDiv = document.getElementById('resultContent');
            
            // 显示测试中状态
            resultDiv.style.display = 'block';
            contentDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-spinner fa-spin me-2"></i>正在测试数据库连接...
                </div>
            `;
            
            // 模拟测试（实际项目中可以通过AJAX调用后端接口）
            setTimeout(() => {
                contentDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>数据库连接测试</h6>
                        <p class="mb-0">
                            <strong>提示:</strong> 要测试真实的数据库连接，请：<br>
                            1. 确保MySQL服务正在运行<br>
                            2. 执行database/init.sql初始化脚本<br>
                            3. 配置正确的数据库连接参数<br>
                            4. 访问游客管理或饭店管理页面进行实际测试
                        </p>
                    </div>
                `;
            }, 2000);
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('基础功能测试页面加载完成');
            console.log('上下文路径:', '${contextPath}');
        });
    </script>
</body>
</html>
