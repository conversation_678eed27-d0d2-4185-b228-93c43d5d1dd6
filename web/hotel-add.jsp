<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String contextPath = request.getContextPath();
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加饭店 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/hotel">返回饭店列表</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-hotel me-2"></i>添加星级饭店</h4>
                    </div>
                    <div class="card-body">
                        <form method="post" action="<%=contextPath%>/hotel" class="needs-validation" novalidate>
                            <input type="hidden" name="action" value="add">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">饭店名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                    <div class="invalid-feedback">请输入饭店名称</div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="starLevel" class="form-label">星级 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="starLevel" name="starLevel" required>
                                        <option value="">选择星级</option>
                                        <option value="5">五星级</option>
                                        <option value="4">四星级</option>
                                        <option value="3">三星级</option>
                                        <option value="2">二星级</option>
                                        <option value="1">一星级</option>
                                    </select>
                                    <div class="invalid-feedback">请选择星级</div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="roomCount" class="form-label">房间数量 <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="roomCount" name="roomCount" 
                                           min="1" max="9999" required>
                                    <div class="invalid-feedback">请输入房间数量</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="address" class="form-label">地址 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="address" name="address" required>
                                    <div class="invalid-feedback">请输入地址</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="phone" class="form-label">电话 <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                    <div class="invalid-feedback">请输入电话</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="manager" class="form-label">负责人</label>
                                    <input type="text" class="form-control" id="manager" name="manager">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="facilities" class="form-label">设施描述</label>
                                <textarea class="form-control" id="facilities" name="facilities" rows="3" 
                                          placeholder="例如：游泳池、健身房、会议室、餐厅、停车场等"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">饭店描述</label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="饭店的详细介绍、特色服务等"></textarea>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="<%=contextPath%>/hotel" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>保存
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
