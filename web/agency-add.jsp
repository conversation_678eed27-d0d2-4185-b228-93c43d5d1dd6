<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String contextPath = request.getContextPath();
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加旅行社 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/agency">返回旅行社列表</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-bus me-2"></i>添加旅行社</h4>
                    </div>
                    <div class="card-body">
                        <form method="post" action="<%=contextPath%>/agency" class="needs-validation" novalidate>
                            <input type="hidden" name="action" value="add">
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="name" class="form-label">旅行社名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                    <div class="invalid-feedback">请输入旅行社名称</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="licenseNumber" class="form-label">营业执照号 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="licenseNumber" name="licenseNumber" required>
                                    <div class="invalid-feedback">请输入营业执照号</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="address" class="form-label">地址 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="address" name="address" required>
                                    <div class="invalid-feedback">请输入地址</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="phone" class="form-label">电话 <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                    <div class="invalid-feedback">请输入电话</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="manager" class="form-label">负责人</label>
                                    <input type="text" class="form-control" id="manager" name="manager">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="businessScope" class="form-label">经营范围</label>
                                <textarea class="form-control" id="businessScope" name="businessScope" rows="3" 
                                          placeholder="例如：国内旅游、出境旅游、入境旅游、会议服务等"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">旅行社描述</label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="旅行社的详细介绍、特色服务等"></textarea>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="<%=contextPath%>/agency" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回
                                </a>
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-save me-2"></i>保存
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
