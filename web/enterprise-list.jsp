<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="com.tourism.model.Enterprise" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    String contextPath = request.getContextPath();
    List<Enterprise> enterprises = (List<Enterprise>) request.getAttribute("enterprises");
    String keyword = (String) request.getAttribute("keyword");
    String type = (String) request.getAttribute("type");
    String status = (String) request.getAttribute("status");
    Boolean activeOnly = (Boolean) request.getAttribute("activeOnly");
    String message = (String) session.getAttribute("message");
    String error = (String) session.getAttribute("error");
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业档案管理 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/">返回首页</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-building me-2"></i>
                        <% if (activeOnly != null && activeOnly) { %>
                            正常企业档案
                        <% } else { %>
                            企业档案管理
                        <% } %>
                    </h2>
                    <div>
                        <% if (activeOnly == null || !activeOnly) { %>
                            <a href="<%=contextPath%>/enterprise?action=active" class="btn btn-outline-success me-2">
                                <i class="fas fa-check me-2"></i>正常企业
                            </a>
                            <a href="<%=contextPath%>/enterprise?action=statistics" class="btn btn-outline-info me-2">
                                <i class="fas fa-chart-bar me-2"></i>统计报表
                            </a>
                        <% } else { %>
                            <a href="<%=contextPath%>/enterprise" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-list me-2"></i>全部企业
                            </a>
                        <% } %>
                        <a href="<%=contextPath%>/enterprise?action=add" class="btn btn-dark">
                            <i class="fas fa-plus me-2"></i>新增企业
                        </a>
                    </div>
                </div>

                <!-- 搜索框 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="get" action="<%=contextPath%>/enterprise">
                            <input type="hidden" name="action" value="search">
                            <div class="row">
                                <div class="col-md-4 mb-2">
                                    <input type="text" class="form-control" name="keyword" 
                                           placeholder="请输入企业名称..." 
                                           value="<%=keyword != null ? keyword : ""%>">
                                </div>
                                <div class="col-md-3 mb-2">
                                    <select class="form-select" name="type">
                                        <option value="">企业类型</option>
                                        <option value="酒店" <%="酒店".equals(type) ? "selected" : ""%>>酒店</option>
                                        <option value="旅行社" <%="旅行社".equals(type) ? "selected" : ""%>>旅行社</option>
                                        <option value="交通运输" <%="交通运输".equals(type) ? "selected" : ""%>>交通运输</option>
                                        <option value="景区景点" <%="景区景点".equals(type) ? "selected" : ""%>>景区景点</option>
                                        <option value="餐饮服务" <%="餐饮服务".equals(type) ? "selected" : ""%>>餐饮服务</option>
                                        <option value="其他" <%="其他".equals(type) ? "selected" : ""%>>其他</option>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <select class="form-select" name="status">
                                        <option value="">企业状态</option>
                                        <option value="正常" <%="正常".equals(status) ? "selected" : ""%>>正常</option>
                                        <option value="注销" <%="注销".equals(status) ? "selected" : ""%>>注销</option>
                                        <option value="吊销" <%="吊销".equals(status) ? "selected" : ""%>>吊销</option>
                                        <option value="停业" <%="停业".equals(status) ? "selected" : ""%>>停业</option>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <button type="submit" class="btn btn-outline-primary w-100" title="搜索">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <div class="col-md-1 mb-2">
                                    <a href="<%=contextPath%>/enterprise" class="btn btn-outline-secondary w-100" title="重置">
                                        <i class="fas fa-refresh"></i>
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 消息提示 -->
                <% if (message != null) { %>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <%=message%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("message"); %>
                <% } %>
                <% if (error != null) { %>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%=error%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("error"); %>
                <% } %>

                <!-- 企业档案列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">企业档案列表</h5>
                    </div>
                    <div class="card-body">
                        <% if (enterprises == null || enterprises.isEmpty()) { %>
                                <div class="text-center py-4">
                                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无企业档案信息</p>
                                    <a href="<%=contextPath%>/enterprise?action=add" class="btn btn-dark">
                                        添加第一个企业档案
                                    </a>
                                </div>
                        <% } else { %>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>ID</th>
                                                <th>企业信息</th>
                                                <th>企业类型</th>
                                                <th>营业执照号</th>
                                                <th>法人代表</th>
                                                <th>联系方式</th>
                                                <th>注册日期</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <% for (Enterprise enterprise : enterprises) { %>
                                                <tr>
                                                    <td><%=enterprise.getId()%></td>
                                                    <td>
                                                        <strong><%=enterprise.getName()%></strong>
                                                        <br><small class="text-muted"><%=enterprise.getAddress()%></small>
                                                    </td>
                                                    <td>
                                                        <% if ("酒店".equals(enterprise.getType())) { %>
                                                            <span class="badge bg-primary"><%=enterprise.getType()%></span>
                                                        <% } else if ("旅行社".equals(enterprise.getType())) { %>
                                                            <span class="badge bg-success"><%=enterprise.getType()%></span>
                                                        <% } else if ("交通运输".equals(enterprise.getType())) { %>
                                                            <span class="badge bg-warning"><%=enterprise.getType()%></span>
                                                        <% } else if ("景区景点".equals(enterprise.getType())) { %>
                                                            <span class="badge bg-info"><%=enterprise.getType()%></span>
                                                        <% } else { %>
                                                            <span class="badge bg-secondary"><%=enterprise.getType()%></span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <code><%=enterprise.getLicenseNumber()%></code>
                                                    </td>
                                                    <td><%=enterprise.getLegalPerson()%></td>
                                                    <td>
                                                        <div><%=enterprise.getPhone()%></div>
                                                        <% if (enterprise.getEmail() != null && !enterprise.getEmail().trim().isEmpty()) { %>
                                                            <small class="text-muted"><%=enterprise.getEmail()%></small>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <% if (enterprise.getRegistrationDate() != null) { %>
                                                            <small><%=sdf.format(enterprise.getRegistrationDate())%></small>
                                                        <% } else { %>
                                                            <span class="text-muted">-</span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <% if ("正常".equals(enterprise.getStatus())) { %>
                                                            <span class="badge bg-success"><%=enterprise.getStatus()%></span>
                                                        <% } else if ("注销".equals(enterprise.getStatus())) { %>
                                                            <span class="badge bg-secondary"><%=enterprise.getStatus()%></span>
                                                        <% } else if ("吊销".equals(enterprise.getStatus())) { %>
                                                            <span class="badge bg-danger"><%=enterprise.getStatus()%></span>
                                                        <% } else { %>
                                                            <span class="badge bg-warning"><%=enterprise.getStatus()%></span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="<%=contextPath%>/enterprise?action=edit&id=<%=enterprise.getId()%>" 
                                                               class="btn btn-sm btn-outline-primary" title="编辑">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="<%=contextPath%>/enterprise?action=delete&id=<%=enterprise.getId()%>" 
                                                               class="btn btn-sm btn-outline-danger" title="删除"
                                                               onclick="return confirm('确定要删除此企业档案吗？此操作不可恢复！')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <% } %>
                                        </tbody>
                                    </table>
                                </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
