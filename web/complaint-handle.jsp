<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.tourism.model.Complaint" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    String contextPath = request.getContextPath();
    Complaint complaint = (Complaint) request.getAttribute("complaint");
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>处理投诉 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/complaint">返回投诉列表</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-cog me-2"></i>处理投诉</h4>
                    </div>
                    <div class="card-body">
                        <!-- 投诉信息展示 -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">投诉详情</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>客户姓名：</strong><%=complaint != null ? complaint.getCustomerName() : ""%></p>
                                        <p><strong>联系电话：</strong><%=complaint != null ? complaint.getCustomerPhone() : ""%></p>
                                        <% if (complaint != null && complaint.getCustomerEmail() != null && !complaint.getCustomerEmail().trim().isEmpty()) { %>
                                            <p><strong>客户邮箱：</strong><%=complaint.getCustomerEmail()%></p>
                                        <% } %>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>投诉类型：</strong>
                                            <span class="badge bg-info"><%=complaint != null ? complaint.getComplaintType() : ""%></span>
                                        </p>
                                        <p><strong>投诉时间：</strong><%=complaint != null ? sdf.format(complaint.getComplaintDate()) : ""%></p>
                                        <p><strong>优先级：</strong>
                                            <% if (complaint != null) { %>
                                                <% if ("紧急".equals(complaint.getPriority())) { %>
                                                    <span class="badge bg-danger"><%=complaint.getPriority()%></span>
                                                <% } else if ("重要".equals(complaint.getPriority())) { %>
                                                    <span class="badge bg-warning"><%=complaint.getPriority()%></span>
                                                <% } else if ("普通".equals(complaint.getPriority())) { %>
                                                    <span class="badge bg-primary"><%=complaint.getPriority()%></span>
                                                <% } else { %>
                                                    <span class="badge bg-secondary"><%=complaint.getPriority()%></span>
                                                <% } %>
                                            <% } %>
                                        </p>
                                    </div>
                                </div>
                                <% if (complaint != null && complaint.getRelatedService() != null && !complaint.getRelatedService().trim().isEmpty()) { %>
                                    <p><strong>相关服务：</strong><%=complaint.getRelatedService()%></p>
                                <% } %>
                                <p><strong>投诉内容：</strong></p>
                                <div class="bg-light p-3 rounded">
                                    <%=complaint != null ? complaint.getComplaintContent() : ""%>
                                </div>
                                <% if (complaint != null && complaint.getDescription() != null && !complaint.getDescription().trim().isEmpty()) { %>
                                    <p class="mt-3"><strong>补充说明：</strong></p>
                                    <div class="bg-light p-3 rounded">
                                        <%=complaint.getDescription()%>
                                    </div>
                                <% } %>
                            </div>
                        </div>

                        <!-- 处理表单 -->
                        <form method="post" action="<%=contextPath%>/complaint" class="needs-validation" novalidate>
                            <input type="hidden" name="action" value="handle">
                            <input type="hidden" name="id" value="<%=complaint != null ? complaint.getId() : ""%>">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="handler" class="form-label">处理人员 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="handler" name="handler" 
                                           value="<%=complaint != null && complaint.getHandler() != null ? complaint.getHandler() : ""%>" required>
                                    <div class="invalid-feedback">请输入处理人员姓名</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">处理状态 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="">请选择处理状态</option>
                                        <option value="处理中" <%=complaint != null && "处理中".equals(complaint.getStatus()) ? "selected" : ""%>>处理中</option>
                                        <option value="已解决" <%=complaint != null && "已解决".equals(complaint.getStatus()) ? "selected" : ""%>>已解决</option>
                                        <option value="已关闭" <%=complaint != null && "已关闭".equals(complaint.getStatus()) ? "selected" : ""%>>已关闭</option>
                                    </select>
                                    <div class="invalid-feedback">请选择处理状态</div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="handleResult" class="form-label">处理结果 <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="handleResult" name="handleResult" rows="6" 
                                          placeholder="请详细描述处理过程和结果..." required><%=complaint != null && complaint.getHandleResult() != null ? complaint.getHandleResult() : ""%></textarea>
                                <div class="invalid-feedback">请输入处理结果</div>
                            </div>
                            
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>处理提醒：</strong>
                                <ul class="mb-0 mt-2">
                                    <li>请认真填写处理结果，确保信息准确完整</li>
                                    <li>处理完成后系统会自动记录处理时间</li>
                                    <li>建议及时与客户沟通处理结果</li>
                                    <li>重要投诉请上报相关负责人</li>
                                </ul>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="<%=contextPath%>/complaint" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check me-2"></i>完成处理
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // 状态变化时的处理建议
        document.getElementById('status').addEventListener('change', function() {
            var status = this.value;
            var handleResult = document.getElementById('handleResult');
            
            switch(status) {
                case '处理中':
                    if (handleResult.value.trim() === '') {
                        handleResult.placeholder = '请描述当前处理进展，预计完成时间，需要客户配合的事项等...';
                    }
                    break;
                case '已解决':
                    if (handleResult.value.trim() === '') {
                        handleResult.placeholder = '请详细描述解决方案，处理结果，客户满意度，后续跟进措施等...';
                    }
                    break;
                case '已关闭':
                    if (handleResult.value.trim() === '') {
                        handleResult.placeholder = '请说明关闭原因，是否已与客户沟通确认，相关资料归档情况等...';
                    }
                    break;
                default:
                    handleResult.placeholder = '请详细描述处理过程和结果...';
            }
        });
    </script>
</body>
</html>
