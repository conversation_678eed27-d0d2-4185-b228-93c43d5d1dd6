<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="com.tourism.model.Complaint" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    String contextPath = request.getContextPath();
    List<Complaint> complaints = (List<Complaint>) request.getAttribute("complaints");
    String keyword = (String) request.getAttribute("keyword");
    String complaintType = (String) request.getAttribute("complaintType");
    String status = (String) request.getAttribute("status");
    String priority = (String) request.getAttribute("priority");
    Boolean pendingOnly = (Boolean) request.getAttribute("pendingOnly");
    String message = (String) session.getAttribute("message");
    String error = (String) session.getAttribute("error");
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投诉管理 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/">返回首页</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-comments me-2"></i>
                        <% if (pendingOnly != null && pendingOnly) { %>
                            待处理投诉
                        <% } else { %>
                            投诉管理
                        <% } %>
                    </h2>
                    <div>
                        <% if (pendingOnly == null || !pendingOnly) { %>
                            <a href="<%=contextPath%>/complaint?action=pending" class="btn btn-outline-warning me-2">
                                <i class="fas fa-clock me-2"></i>待处理投诉
                            </a>
                            <a href="<%=contextPath%>/complaint?action=statistics" class="btn btn-outline-info me-2">
                                <i class="fas fa-chart-bar me-2"></i>统计报表
                            </a>
                        <% } else { %>
                            <a href="<%=contextPath%>/complaint" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-list me-2"></i>全部投诉
                            </a>
                        <% } %>
                        <a href="<%=contextPath%>/complaint?action=add" class="btn btn-secondary">
                            <i class="fas fa-plus me-2"></i>新增投诉
                        </a>
                    </div>
                </div>

                <!-- 搜索框 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="get" action="<%=contextPath%>/complaint">
                            <input type="hidden" name="action" value="search">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <input type="text" class="form-control" name="keyword" 
                                           placeholder="请输入客户姓名..." 
                                           value="<%=keyword != null ? keyword : ""%>">
                                </div>
                                <div class="col-md-2 mb-2">
                                    <select class="form-select" name="complaintType">
                                        <option value="">投诉类型</option>
                                        <option value="服务质量" <%="服务质量".equals(complaintType) ? "selected" : ""%>>服务质量</option>
                                        <option value="价格问题" <%="价格问题".equals(complaintType) ? "selected" : ""%>>价格问题</option>
                                        <option value="安全问题" <%="安全问题".equals(complaintType) ? "selected" : ""%>>安全问题</option>
                                        <option value="设施问题" <%="设施问题".equals(complaintType) ? "selected" : ""%>>设施问题</option>
                                        <option value="行程安排" <%="行程安排".equals(complaintType) ? "selected" : ""%>>行程安排</option>
                                        <option value="其他" <%="其他".equals(complaintType) ? "selected" : ""%>>其他</option>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <select class="form-select" name="status">
                                        <option value="">处理状态</option>
                                        <option value="待处理" <%="待处理".equals(status) ? "selected" : ""%>>待处理</option>
                                        <option value="处理中" <%="处理中".equals(status) ? "selected" : ""%>>处理中</option>
                                        <option value="已解决" <%="已解决".equals(status) ? "selected" : ""%>>已解决</option>
                                        <option value="已关闭" <%="已关闭".equals(status) ? "selected" : ""%>>已关闭</option>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <select class="form-select" name="priority">
                                        <option value="">优先级</option>
                                        <option value="紧急" <%="紧急".equals(priority) ? "selected" : ""%>>紧急</option>
                                        <option value="重要" <%="重要".equals(priority) ? "selected" : ""%>>重要</option>
                                        <option value="普通" <%="普通".equals(priority) ? "selected" : ""%>>普通</option>
                                        <option value="低" <%="低".equals(priority) ? "selected" : ""%>>低</option>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <button type="submit" class="btn btn-outline-primary w-100" title="搜索">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <div class="col-md-1 mb-2">
                                    <a href="<%=contextPath%>/complaint" class="btn btn-outline-secondary w-100" title="重置">
                                        <i class="fas fa-refresh"></i>
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 消息提示 -->
                <% if (message != null) { %>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <%=message%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("message"); %>
                <% } %>
                <% if (error != null) { %>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%=error%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("error"); %>
                <% } %>

                <!-- 投诉列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">投诉列表</h5>
                    </div>
                    <div class="card-body">
                        <% if (complaints == null || complaints.isEmpty()) { %>
                                <div class="text-center py-4">
                                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无投诉信息</p>
                                    <a href="<%=contextPath%>/complaint?action=add" class="btn btn-secondary">
                                        添加第一个投诉
                                    </a>
                                </div>
                        <% } else { %>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>ID</th>
                                                <th>客户信息</th>
                                                <th>投诉类型</th>
                                                <th>投诉内容</th>
                                                <th>相关服务</th>
                                                <th>投诉时间</th>
                                                <th>优先级</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <% for (Complaint complaint : complaints) { %>
                                                <tr>
                                                    <td><%=complaint.getId()%></td>
                                                    <td>
                                                        <strong><%=complaint.getCustomerName()%></strong>
                                                        <br><small class="text-muted"><%=complaint.getCustomerPhone()%></small>
                                                        <% if (complaint.getCustomerEmail() != null && !complaint.getCustomerEmail().trim().isEmpty()) { %>
                                                            <br><small class="text-muted"><%=complaint.getCustomerEmail()%></small>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info"><%=complaint.getComplaintType()%></span>
                                                    </td>
                                                    <td>
                                                        <div style="max-width: 200px;">
                                                            <%=complaint.getComplaintContent().length() > 50 ? 
                                                               complaint.getComplaintContent().substring(0, 50) + "..." : 
                                                               complaint.getComplaintContent()%>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <% if (complaint.getRelatedService() != null && !complaint.getRelatedService().trim().isEmpty()) { %>
                                                            <small><%=complaint.getRelatedService()%></small>
                                                        <% } else { %>
                                                            <span class="text-muted">-</span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <small><%=sdf.format(complaint.getComplaintDate())%></small>
                                                    </td>
                                                    <td>
                                                        <% if ("紧急".equals(complaint.getPriority())) { %>
                                                            <span class="badge bg-danger"><%=complaint.getPriority()%></span>
                                                        <% } else if ("重要".equals(complaint.getPriority())) { %>
                                                            <span class="badge bg-warning"><%=complaint.getPriority()%></span>
                                                        <% } else if ("普通".equals(complaint.getPriority())) { %>
                                                            <span class="badge bg-primary"><%=complaint.getPriority()%></span>
                                                        <% } else { %>
                                                            <span class="badge bg-secondary"><%=complaint.getPriority()%></span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <% if ("待处理".equals(complaint.getStatus())) { %>
                                                            <span class="badge bg-warning"><%=complaint.getStatus()%></span>
                                                        <% } else if ("处理中".equals(complaint.getStatus())) { %>
                                                            <span class="badge bg-info"><%=complaint.getStatus()%></span>
                                                        <% } else if ("已解决".equals(complaint.getStatus())) { %>
                                                            <span class="badge bg-success"><%=complaint.getStatus()%></span>
                                                        <% } else { %>
                                                            <span class="badge bg-secondary"><%=complaint.getStatus()%></span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <% if ("待处理".equals(complaint.getStatus()) || "处理中".equals(complaint.getStatus())) { %>
                                                                <a href="<%=contextPath%>/complaint?action=handle&id=<%=complaint.getId()%>" 
                                                                   class="btn btn-sm btn-outline-success" title="处理">
                                                                    <i class="fas fa-cog"></i>
                                                                </a>
                                                            <% } %>
                                                            <a href="<%=contextPath%>/complaint?action=edit&id=<%=complaint.getId()%>" 
                                                               class="btn btn-sm btn-outline-primary" title="编辑">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="<%=contextPath%>/complaint?action=delete&id=<%=complaint.getId()%>" 
                                                               class="btn btn-sm btn-outline-danger" title="删除"
                                                               onclick="return confirm('确定要删除此投诉记录吗？此操作不可恢复！')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <% } %>
                                        </tbody>
                                    </table>
                                </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
