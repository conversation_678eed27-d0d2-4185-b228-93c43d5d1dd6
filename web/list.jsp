<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="com.tourism.model.Tourist" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    String contextPath = request.getContextPath();
    List<Tourist> tourists = (List<Tourist>) request.getAttribute("tourists");
    String keyword = (String) request.getAttribute("keyword");
    String message = (String) session.getAttribute("message");
    String error = (String) session.getAttribute("error");
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游客管理 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/">返回首页</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-users me-2"></i>游客管理</h2>
                    <a href="<%=contextPath%>/tourist?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>添加游客
                    </a>
                </div>

                <!-- 搜索框 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="get" action="<%=contextPath%>/tourist">
                            <input type="hidden" name="action" value="search">
                            <div class="row">
                                <div class="col-md-10">
                                    <input type="text" class="form-control" name="keyword"
                                           placeholder="请输入游客姓名进行搜索..."
                                           value="<%=keyword != null ? keyword : ""%>">
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-search me-2"></i>搜索
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 消息提示 -->
                <% if (message != null) { %>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <%=message%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("message"); %>
                <% } %>
                <% if (error != null) { %>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%=error%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("error"); %>
                <% } %>

                <!-- 游客列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">游客列表</h5>
                    </div>
                    <div class="card-body">
                        <% if (tourists == null || tourists.isEmpty()) { %>
                                <div class="text-center py-4">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无游客信息</p>
                                    <a href="<%=contextPath%>/tourist?action=add" class="btn btn-primary">
                                        添加第一个游客
                                    </a>
                                </div>
                        <% } else { %>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>ID</th>
                                                <th>姓名</th>
                                                <th>身份证号</th>
                                                <th>电话</th>
                                                <th>邮箱</th>
                                                <th>地址</th>
                                                <th>注册时间</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <% for (Tourist tourist : tourists) { %>
                                                <tr>
                                                    <td><%=tourist.getId()%></td>
                                                    <td><%=tourist.getName()%></td>
                                                    <td><%=tourist.getIdCard()%></td>
                                                    <td><%=tourist.getPhone()%></td>
                                                    <td><%=tourist.getEmail() != null ? tourist.getEmail() : ""%></td>
                                                    <td><%=tourist.getAddress() != null ? tourist.getAddress() : ""%></td>
                                                    <td>
                                                        <% if (tourist.getRegisterDate() != null) { %>
                                                            <%=sdf.format(tourist.getRegisterDate())%>
                                                        <% } else { %>
                                                            <span class="text-muted">未知</span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <% if ("活跃".equals(tourist.getStatus())) { %>
                                                            <span class="badge bg-success"><%=tourist.getStatus()%></span>
                                                        <% } else { %>
                                                            <span class="badge bg-secondary"><%=tourist.getStatus()%></span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="<%=contextPath%>/tourist?action=edit&id=<%=tourist.getId()%>"
                                                               class="btn btn-sm btn-outline-primary" title="编辑">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="<%=contextPath%>/tourist?action=delete&id=<%=tourist.getId()%>"
                                                               class="btn btn-sm btn-outline-danger" title="删除"
                                                               onclick="return confirm('确定要删除游客「<%=tourist.getName()%>」吗？此操作不可恢复！')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <% } %>
                                        </tbody>
                                    </table>
                                </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
