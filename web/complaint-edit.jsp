<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.tourism.model.Complaint" %>
<%
    String contextPath = request.getContextPath();
    Complaint complaint = (Complaint) request.getAttribute("complaint");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑投诉 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/complaint">返回投诉列表</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-edit me-2"></i>编辑投诉</h4>
                    </div>
                    <div class="card-body">
                        <form method="post" action="<%=contextPath%>/complaint" class="needs-validation" novalidate>
                            <input type="hidden" name="action" value="update">
                            <input type="hidden" name="id" value="<%=complaint != null ? complaint.getId() : ""%>">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="customerName" class="form-label">客户姓名 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="customerName" name="customerName" 
                                           value="<%=complaint != null ? complaint.getCustomerName() : ""%>" required>
                                    <div class="invalid-feedback">请输入客户姓名</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="customerPhone" class="form-label">联系电话 <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="customerPhone" name="customerPhone" 
                                           value="<%=complaint != null ? complaint.getCustomerPhone() : ""%>"
                                           pattern="[0-9]{11}" title="请输入11位手机号" required>
                                    <div class="invalid-feedback">请输入正确的手机号</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="customerEmail" class="form-label">客户邮箱</label>
                                    <input type="email" class="form-control" id="customerEmail" name="customerEmail" 
                                           value="<%=complaint != null && complaint.getCustomerEmail() != null ? complaint.getCustomerEmail() : ""%>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="complaintType" class="form-label">投诉类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="complaintType" name="complaintType" required>
                                        <option value="">请选择投诉类型</option>
                                        <option value="服务质量" <%=complaint != null && "服务质量".equals(complaint.getComplaintType()) ? "selected" : ""%>>服务质量</option>
                                        <option value="价格问题" <%=complaint != null && "价格问题".equals(complaint.getComplaintType()) ? "selected" : ""%>>价格问题</option>
                                        <option value="安全问题" <%=complaint != null && "安全问题".equals(complaint.getComplaintType()) ? "selected" : ""%>>安全问题</option>
                                        <option value="设施问题" <%=complaint != null && "设施问题".equals(complaint.getComplaintType()) ? "selected" : ""%>>设施问题</option>
                                        <option value="行程安排" <%=complaint != null && "行程安排".equals(complaint.getComplaintType()) ? "selected" : ""%>>行程安排</option>
                                        <option value="其他" <%=complaint != null && "其他".equals(complaint.getComplaintType()) ? "selected" : ""%>>其他</option>
                                    </select>
                                    <div class="invalid-feedback">请选择投诉类型</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="relatedService" class="form-label">相关服务</label>
                                    <input type="text" class="form-control" id="relatedService" name="relatedService" 
                                           value="<%=complaint != null && complaint.getRelatedService() != null ? complaint.getRelatedService() : ""%>"
                                           placeholder="例如：酒店预订、导游服务、交通安排等">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="priority" class="form-label">优先级</label>
                                    <select class="form-select" id="priority" name="priority">
                                        <option value="普通" <%=complaint != null && "普通".equals(complaint.getPriority()) ? "selected" : ""%>>普通</option>
                                        <option value="重要" <%=complaint != null && "重要".equals(complaint.getPriority()) ? "selected" : ""%>>重要</option>
                                        <option value="紧急" <%=complaint != null && "紧急".equals(complaint.getPriority()) ? "selected" : ""%>>紧急</option>
                                        <option value="低" <%=complaint != null && "低".equals(complaint.getPriority()) ? "selected" : ""%>>低</option>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="status" class="form-label">状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="待处理" <%=complaint != null && "待处理".equals(complaint.getStatus()) ? "selected" : ""%>>待处理</option>
                                        <option value="处理中" <%=complaint != null && "处理中".equals(complaint.getStatus()) ? "selected" : ""%>>处理中</option>
                                        <option value="已解决" <%=complaint != null && "已解决".equals(complaint.getStatus()) ? "selected" : ""%>>已解决</option>
                                        <option value="已关闭" <%=complaint != null && "已关闭".equals(complaint.getStatus()) ? "selected" : ""%>>已关闭</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="complaintContent" class="form-label">投诉内容 <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="complaintContent" name="complaintContent" rows="5" 
                                          placeholder="请详细描述您遇到的问题..." required><%=complaint != null ? complaint.getComplaintContent() : ""%></textarea>
                                <div class="invalid-feedback">请输入投诉内容</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">补充说明</label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="其他需要说明的情况..."><%=complaint != null && complaint.getDescription() != null ? complaint.getDescription() : ""%></textarea>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="<%=contextPath%>/complaint" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回
                                </a>
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-save me-2"></i>更新投诉
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
