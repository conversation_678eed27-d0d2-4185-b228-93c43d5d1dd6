<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.tourism.model.Enterprise" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    String contextPath = request.getContextPath();
    Enterprise enterprise = (Enterprise) request.getAttribute("enterprise");
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑企业档案 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/enterprise">返回企业档案</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-edit me-2"></i>编辑企业档案</h4>
                    </div>
                    <div class="card-body">
                        <form method="post" action="<%=contextPath%>/enterprise" class="needs-validation" novalidate>
                            <input type="hidden" name="action" value="update">
                            <input type="hidden" name="id" value="<%=enterprise != null ? enterprise.getId() : ""%>">
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="name" class="form-label">企业名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<%=enterprise != null ? enterprise.getName() : ""%>" required>
                                    <div class="invalid-feedback">请输入企业名称</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="type" class="form-label">企业类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="type" name="type" required>
                                        <option value="">请选择企业类型</option>
                                        <option value="酒店" <%=enterprise != null && "酒店".equals(enterprise.getType()) ? "selected" : ""%>>酒店</option>
                                        <option value="旅行社" <%=enterprise != null && "旅行社".equals(enterprise.getType()) ? "selected" : ""%>>旅行社</option>
                                        <option value="交通运输" <%=enterprise != null && "交通运输".equals(enterprise.getType()) ? "selected" : ""%>>交通运输</option>
                                        <option value="景区景点" <%=enterprise != null && "景区景点".equals(enterprise.getType()) ? "selected" : ""%>>景区景点</option>
                                        <option value="餐饮服务" <%=enterprise != null && "餐饮服务".equals(enterprise.getType()) ? "selected" : ""%>>餐饮服务</option>
                                        <option value="其他" <%=enterprise != null && "其他".equals(enterprise.getType()) ? "selected" : ""%>>其他</option>
                                    </select>
                                    <div class="invalid-feedback">请选择企业类型</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="licenseNumber" class="form-label">营业执照号 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="licenseNumber" name="licenseNumber" 
                                           value="<%=enterprise != null ? enterprise.getLicenseNumber() : ""%>"
                                           pattern="[0-9A-Z]{18}" title="请输入18位营业执照号" required>
                                    <div class="invalid-feedback">请输入正确的营业执照号</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="legalPerson" class="form-label">法人代表 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="legalPerson" name="legalPerson" 
                                           value="<%=enterprise != null ? enterprise.getLegalPerson() : ""%>" required>
                                    <div class="invalid-feedback">请输入法人代表姓名</div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">企业地址 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="address" name="address" 
                                       value="<%=enterprise != null ? enterprise.getAddress() : ""%>" required>
                                <div class="invalid-feedback">请输入企业地址</div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">联系电话 <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<%=enterprise != null ? enterprise.getPhone() : ""%>"
                                           pattern="[0-9\-]{7,20}" title="请输入正确的电话号码" required>
                                    <div class="invalid-feedback">请输入正确的联系电话</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">企业邮箱</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<%=enterprise != null && enterprise.getEmail() != null ? enterprise.getEmail() : ""%>">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="registrationDate" class="form-label">注册日期</label>
                                    <input type="date" class="form-control" id="registrationDate" name="registrationDate" 
                                           value="<%=enterprise != null && enterprise.getRegistrationDate() != null ? sdf.format(enterprise.getRegistrationDate()) : ""%>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">企业状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="正常" <%=enterprise != null && "正常".equals(enterprise.getStatus()) ? "selected" : ""%>>正常</option>
                                        <option value="注销" <%=enterprise != null && "注销".equals(enterprise.getStatus()) ? "selected" : ""%>>注销</option>
                                        <option value="吊销" <%=enterprise != null && "吊销".equals(enterprise.getStatus()) ? "selected" : ""%>>吊销</option>
                                        <option value="停业" <%=enterprise != null && "停业".equals(enterprise.getStatus()) ? "selected" : ""%>>停业</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="businessScope" class="form-label">经营范围</label>
                                <textarea class="form-control" id="businessScope" name="businessScope" rows="3" 
                                          placeholder="请描述企业的主要经营范围..."><%=enterprise != null && enterprise.getBusinessScope() != null ? enterprise.getBusinessScope() : ""%></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">企业描述</label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="其他需要说明的企业信息..."><%=enterprise != null && enterprise.getDescription() != null ? enterprise.getDescription() : ""%></textarea>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="<%=contextPath%>/enterprise" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回
                                </a>
                                <button type="submit" class="btn btn-dark">
                                    <i class="fas fa-save me-2"></i>更新企业档案
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // 营业执照号格式化
        document.getElementById('licenseNumber').addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    </script>
</body>
</html>
