<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.tourism.model.Hotel" %>
<%
    String contextPath = request.getContextPath();
    Hotel hotel = (Hotel) request.getAttribute("hotel");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑饭店 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/hotel">返回饭店列表</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-edit me-2"></i>编辑星级饭店</h4>
                    </div>
                    <div class="card-body">
                        <form method="post" action="<%=contextPath%>/hotel" class="needs-validation" novalidate>
                            <input type="hidden" name="action" value="update">
                            <input type="hidden" name="id" value="<%=hotel != null ? hotel.getId() : ""%>">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">饭店名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="<%=hotel != null ? hotel.getName() : ""%>" required>
                                    <div class="invalid-feedback">请输入饭店名称</div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="starLevel" class="form-label">星级 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="starLevel" name="starLevel" required>
                                        <option value="">选择星级</option>
                                        <option value="5" <%=hotel != null && hotel.getStarLevel() == 5 ? "selected" : ""%>>五星级</option>
                                        <option value="4" <%=hotel != null && hotel.getStarLevel() == 4 ? "selected" : ""%>>四星级</option>
                                        <option value="3" <%=hotel != null && hotel.getStarLevel() == 3 ? "selected" : ""%>>三星级</option>
                                        <option value="2" <%=hotel != null && hotel.getStarLevel() == 2 ? "selected" : ""%>>二星级</option>
                                        <option value="1" <%=hotel != null && hotel.getStarLevel() == 1 ? "selected" : ""%>>一星级</option>
                                    </select>
                                    <div class="invalid-feedback">请选择星级</div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="roomCount" class="form-label">房间数量 <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="roomCount" name="roomCount"
                                           value="<%=hotel != null ? hotel.getRoomCount() : ""%>" min="1" max="9999" required>
                                    <div class="invalid-feedback">请输入房间数量</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="address" class="form-label">地址 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="address" name="address"
                                           value="<%=hotel != null ? hotel.getAddress() : ""%>" required>
                                    <div class="invalid-feedback">请输入地址</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="phone" class="form-label">电话 <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="<%=hotel != null ? hotel.getPhone() : ""%>" required>
                                    <div class="invalid-feedback">请输入电话</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="email" class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<%=hotel != null && hotel.getEmail() != null ? hotel.getEmail() : ""%>">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="manager" class="form-label">负责人</label>
                                    <input type="text" class="form-control" id="manager" name="manager"
                                           value="<%=hotel != null && hotel.getManager() != null ? hotel.getManager() : ""%>">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="status" class="form-label">状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="营业" <%=hotel != null && "营业".equals(hotel.getStatus()) ? "selected" : ""%>>营业</option>
                                        <option value="停业" <%=hotel != null && "停业".equals(hotel.getStatus()) ? "selected" : ""%>>停业</option>
                                        <option value="装修" <%=hotel != null && "装修".equals(hotel.getStatus()) ? "selected" : ""%>>装修</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="facilities" class="form-label">设施描述</label>
                                <textarea class="form-control" id="facilities" name="facilities" rows="3"
                                          placeholder="例如：游泳池、健身房、会议室、餐厅、停车场等"><%=hotel != null && hotel.getFacilities() != null ? hotel.getFacilities() : ""%></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">饭店描述</label>
                                <textarea class="form-control" id="description" name="description" rows="3"
                                          placeholder="饭店的详细介绍、特色服务等"><%=hotel != null && hotel.getDescription() != null ? hotel.getDescription() : ""%></textarea>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="<%=contextPath%>/hotel" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>更新
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
