<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="com.tourism.model.TravelAgency" %>
<%
    String contextPath = request.getContextPath();
    List<TravelAgency> agencies = (List<TravelAgency>) request.getAttribute("agencies");
    String keyword = (String) request.getAttribute("keyword");
    String status = (String) request.getAttribute("status");
    String message = (String) session.getAttribute("message");
    String error = (String) session.getAttribute("error");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>旅行社管理 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/">返回首页</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-bus me-2"></i>旅行社管理</h2>
                    <a href="<%=contextPath%>/agency?action=add" class="btn btn-info">
                        <i class="fas fa-plus me-2"></i>添加旅行社
                    </a>
                </div>

                <!-- 搜索框 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="get" action="<%=contextPath%>/agency">
                            <input type="hidden" name="action" value="search">
                            <div class="row">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" name="keyword" 
                                           placeholder="请输入旅行社名称..." 
                                           value="<%=keyword != null ? keyword : ""%>">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" name="status">
                                        <option value="">选择状态</option>
                                        <option value="营业" <%="营业".equals(status) ? "selected" : ""%>>营业</option>
                                        <option value="停业" <%="停业".equals(status) ? "selected" : ""%>>停业</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-search me-2"></i>搜索
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <a href="<%=contextPath%>/agency" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-refresh me-2"></i>重置
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 消息提示 -->
                <% if (message != null) { %>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <%=message%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("message"); %>
                <% } %>
                <% if (error != null) { %>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%=error%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("error"); %>
                <% } %>

                <!-- 旅行社列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">旅行社列表</h5>
                    </div>
                    <div class="card-body">
                        <% if (agencies == null || agencies.isEmpty()) { %>
                                <div class="text-center py-4">
                                    <i class="fas fa-bus fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无旅行社信息</p>
                                    <a href="<%=contextPath%>/agency?action=add" class="btn btn-info">
                                        添加第一个旅行社
                                    </a>
                                </div>
                        <% } else { %>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>ID</th>
                                                <th>旅行社名称</th>
                                                <th>营业执照号</th>
                                                <th>地址</th>
                                                <th>电话</th>
                                                <th>负责人</th>
                                                <th>经营范围</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <% for (TravelAgency agency : agencies) { %>
                                                <tr>
                                                    <td><%=agency.getId()%></td>
                                                    <td>
                                                        <strong><%=agency.getName()%></strong>
                                                        <% if (agency.getDescription() != null && !agency.getDescription().trim().isEmpty()) { %>
                                                            <br><small class="text-muted"><%=agency.getDescription()%></small>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <code><%=agency.getLicenseNumber()%></code>
                                                    </td>
                                                    <td><%=agency.getAddress()%></td>
                                                    <td><%=agency.getPhone()%></td>
                                                    <td><%=agency.getManager() != null ? agency.getManager() : ""%></td>
                                                    <td>
                                                        <% if (agency.getBusinessScope() != null && !agency.getBusinessScope().trim().isEmpty()) { %>
                                                            <small><%=agency.getBusinessScope()%></small>
                                                        <% } else { %>
                                                            <span class="text-muted">-</span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <% if ("营业".equals(agency.getStatus())) { %>
                                                            <span class="badge bg-success"><%=agency.getStatus()%></span>
                                                        <% } else { %>
                                                            <span class="badge bg-secondary"><%=agency.getStatus()%></span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="<%=contextPath%>/agency?action=edit&id=<%=agency.getId()%>" 
                                                               class="btn btn-sm btn-outline-primary" title="编辑">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="<%=contextPath%>/agency?action=delete&id=<%=agency.getId()%>" 
                                                               class="btn btn-sm btn-outline-danger" title="删除"
                                                               onclick="return confirm('确定要删除旅行社「<%=agency.getName()%>」吗？此操作不可恢复！')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <% } %>
                                        </tbody>
                                    </table>
                                </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
