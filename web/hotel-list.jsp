<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="com.tourism.model.Hotel" %>
<%
    String contextPath = request.getContextPath();
    List<Hotel> hotels = (List<Hotel>) request.getAttribute("hotels");
    String keyword = (String) request.getAttribute("keyword");
    String starLevel = (String) request.getAttribute("starLevel");
    String message = (String) session.getAttribute("message");
    String error = (String) session.getAttribute("error");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星级饭店管理 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/">返回首页</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-hotel me-2"></i>星级饭店管理</h2>
                    <a href="<%=contextPath%>/hotel?action=add" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>添加饭店
                    </a>
                </div>

                <!-- 搜索框 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="get" action="<%=contextPath%>/hotel">
                            <input type="hidden" name="action" value="search">
                            <div class="row">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" name="keyword"
                                           placeholder="请输入饭店名称..."
                                           value="<%=keyword != null ? keyword : ""%>">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" name="starLevel">
                                        <option value="">选择星级</option>
                                        <option value="5" <%="5".equals(starLevel) ? "selected" : ""%>>五星级</option>
                                        <option value="4" <%="4".equals(starLevel) ? "selected" : ""%>>四星级</option>
                                        <option value="3" <%="3".equals(starLevel) ? "selected" : ""%>>三星级</option>
                                        <option value="2" <%="2".equals(starLevel) ? "selected" : ""%>>二星级</option>
                                        <option value="1" <%="1".equals(starLevel) ? "selected" : ""%>>一星级</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-search me-2"></i>搜索
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <a href="<%=contextPath%>/hotel" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-refresh me-2"></i>重置
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 消息提示 -->
                <% if (message != null) { %>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <%=message%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("message"); %>
                <% } %>
                <% if (error != null) { %>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%=error%>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <% session.removeAttribute("error"); %>
                <% } %>

                <!-- 饭店列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">饭店列表</h5>
                    </div>
                    <div class="card-body">
                        <% if (hotels == null || hotels.isEmpty()) { %>
                                <div class="text-center py-4">
                                    <i class="fas fa-hotel fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无饭店信息</p>
                                    <a href="<%=contextPath%>/hotel?action=add" class="btn btn-success">
                                        添加第一个饭店
                                    </a>
                                </div>
                        <% } else { %>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>ID</th>
                                                <th>饭店名称</th>
                                                <th>星级</th>
                                                <th>地址</th>
                                                <th>电话</th>
                                                <th>房间数</th>
                                                <th>负责人</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <% for (Hotel hotel : hotels) { %>
                                                <tr>
                                                    <td><%=hotel.getId()%></td>
                                                    <td>
                                                        <strong><%=hotel.getName()%></strong>
                                                        <% if (hotel.getDescription() != null && !hotel.getDescription().trim().isEmpty()) { %>
                                                            <br><small class="text-muted"><%=hotel.getDescription()%></small>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <% for (int i = 1; i <= hotel.getStarLevel(); i++) { %>
                                                            <i class="fas fa-star text-warning"></i>
                                                        <% } %>
                                                        <% for (int i = hotel.getStarLevel() + 1; i <= 5; i++) { %>
                                                            <i class="far fa-star text-muted"></i>
                                                        <% } %>
                                                        <br><small class="text-muted"><%=hotel.getStarLevel()%>星级</small>
                                                    </td>
                                                    <td><%=hotel.getAddress()%></td>
                                                    <td><%=hotel.getPhone()%></td>
                                                    <td>
                                                        <span class="badge bg-info"><%=hotel.getRoomCount()%>间</span>
                                                    </td>
                                                    <td><%=hotel.getManager() != null ? hotel.getManager() : ""%></td>
                                                    <td>
                                                        <% if ("营业".equals(hotel.getStatus())) { %>
                                                            <span class="badge bg-success"><%=hotel.getStatus()%></span>
                                                        <% } else if ("装修".equals(hotel.getStatus())) { %>
                                                            <span class="badge bg-warning"><%=hotel.getStatus()%></span>
                                                        <% } else { %>
                                                            <span class="badge bg-secondary"><%=hotel.getStatus()%></span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="<%=contextPath%>/hotel?action=edit&id=<%=hotel.getId()%>"
                                                               class="btn btn-sm btn-outline-primary" title="编辑">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="<%=contextPath%>/hotel?action=delete&id=<%=hotel.getId()%>"
                                                               class="btn btn-sm btn-outline-danger" title="删除"
                                                               onclick="return confirm('确定要删除饭店「<%=hotel.getName()%>」吗？此操作不可恢复！')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <% } %>
                                        </tbody>
                                    </table>
                                </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
