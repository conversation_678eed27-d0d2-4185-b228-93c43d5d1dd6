<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速修复指南 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .step-card { margin-bottom: 20px; border-left: 4px solid #007bff; }
        .download-link { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .file-path { background: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-danger">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-tools me-2"></i>快速修复指南
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="${pageContext.request.contextPath}/">
                    <i class="fas fa-home me-1"></i>返回首页
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 问题诊断 -->
        <div class="alert alert-danger">
            <h4><i class="fas fa-exclamation-triangle me-2"></i>发现问题</h4>
            <ul class="mb-0">
                <li><strong>缺少MySQL JDBC驱动</strong> - 导致数据库连接失败</li>
                <li><strong>数据无法显示</strong> - 所有管理模块都显示"暂无信息"</li>
                <li><strong>404错误</strong> - 部分页面无法访问</li>
            </ul>
        </div>

        <!-- 解决方案 -->
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-wrench text-primary me-2"></i>解决方案</h2>
            </div>
        </div>

        <!-- 步骤1：下载MySQL驱动 -->
        <div class="card step-card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-download me-2"></i>步骤1：下载MySQL JDBC驱动</h5>
            </div>
            <div class="card-body">
                <p><strong>选择以下任一方式下载：</strong></p>
                
                <div class="download-link">
                    <h6><i class="fas fa-star text-warning me-1"></i>方式1：Maven Central（推荐）</h6>
                    <p>访问：<a href="https://search.maven.org/artifact/mysql/mysql-connector-java/8.0.33/jar" target="_blank">
                        https://search.maven.org/artifact/mysql/mysql-connector-java/8.0.33/jar
                    </a></p>
                    <p>点击右侧的 <span class="badge bg-success">jar</span> 按钮下载</p>
                </div>

                <div class="download-link">
                    <h6><i class="fas fa-globe me-1"></i>方式2：MySQL官网</h6>
                    <p>访问：<a href="https://dev.mysql.com/downloads/connector/j/" target="_blank">
                        https://dev.mysql.com/downloads/connector/j/
                    </a></p>
                    <p>选择 <strong>Platform Independent</strong> → <strong>ZIP Archive</strong></p>
                </div>

                <div class="download-link">
                    <h6><i class="fas fa-link me-1"></i>方式3：直接下载链接</h6>
                    <p>右键另存为：<a href="https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar" target="_blank">
                        mysql-connector-java-8.0.33.jar
                    </a></p>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>文件名要求：</strong>下载后确保文件名为 <code>mysql-connector-java-8.0.33.jar</code>
                </div>
            </div>
        </div>

        <!-- 步骤2：放置文件 -->
        <div class="card step-card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-folder me-2"></i>步骤2：放置驱动文件</h5>
            </div>
            <div class="card-body">
                <p><strong>将下载的JAR文件复制到以下两个位置：</strong></p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>位置1：开发环境</h6>
                        <div class="file-path">
                            E:/IntelliJidea/project_idea/webtest/lib/<br>
                            mysql-connector-java-8.0.33.jar
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>位置2：运行环境</h6>
                        <div class="file-path">
                            E:/IntelliJidea/project_idea/webtest/web/WEB-INF/lib/<br>
                            mysql-connector-java-8.0.33.jar
                        </div>
                    </div>
                </div>

                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>重要：</strong>必须同时放置在两个位置，缺一不可！
                </div>
            </div>
        </div>

        <!-- 步骤3：准备数据库 -->
        <div class="card step-card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-database me-2"></i>步骤3：准备数据库</h5>
            </div>
            <div class="card-body">
                <h6>3.1 启动MySQL服务</h6>
                <p>确保MySQL服务正在运行（通常在系统服务中）</p>

                <h6>3.2 创建数据库</h6>
                <div class="file-path">
                    mysql -u root -p<br>
                    CREATE DATABASE tourism_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;<br>
                    exit;
                </div>

                <h6>3.3 初始化数据表</h6>
                <div class="file-path">
                    mysql -u root -p tourism_system < database/init.sql
                </div>

                <h6>3.4 检查数据库配置</h6>
                <p>确认 <code>src/database.properties</code> 中的密码设置正确：</p>
                <div class="file-path">
                    db.password=root  # 改为你的MySQL密码
                </div>
            </div>
        </div>

        <!-- 步骤4：重启和测试 -->
        <div class="card step-card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="fas fa-refresh me-2"></i>步骤4：重启和测试</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li><strong>重启应用服务器</strong>（Tomcat等）</li>
                    <li><strong>测试数据库连接：</strong>
                        <a href="${pageContext.request.contextPath}/test-database.jsp" class="btn btn-sm btn-primary" target="_blank">
                            <i class="fas fa-test-tube me-1"></i>测试连接
                        </a>
                    </li>
                    <li><strong>测试功能模块：</strong>
                        <div class="btn-group mt-2" role="group">
                            <a href="${pageContext.request.contextPath}/tourist" class="btn btn-sm btn-outline-primary">游客管理</a>
                            <a href="${pageContext.request.contextPath}/hotel" class="btn btn-sm btn-outline-success">饭店管理</a>
                            <a href="${pageContext.request.contextPath}/guide" class="btn btn-sm btn-outline-info">导游管理</a>
                            <a href="${pageContext.request.contextPath}/agency" class="btn btn-sm btn-outline-warning">旅行社管理</a>
                        </div>
                    </li>
                </ol>
            </div>
        </div>

        <!-- 验证清单 -->
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0"><i class="fas fa-check-square me-2"></i>完成验证清单</h5>
            </div>
            <div class="card-body">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check1">
                    <label class="form-check-label" for="check1">
                        MySQL JDBC驱动已下载并放置在正确位置
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check2">
                    <label class="form-check-label" for="check2">
                        MySQL服务已启动
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check3">
                    <label class="form-check-label" for="check3">
                        tourism_system数据库已创建
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check4">
                    <label class="form-check-label" for="check4">
                        数据表已初始化（运行init.sql）
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check5">
                    <label class="form-check-label" for="check5">
                        数据库配置文件密码正确
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check6">
                    <label class="form-check-label" for="check6">
                        应用服务器已重启
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check7">
                    <label class="form-check-label" for="check7">
                        数据库连接测试成功
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check8">
                    <label class="form-check-label" for="check8">
                        各管理模块能正常显示数据
                    </label>
                </div>
            </div>
        </div>

        <!-- 常见问题 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-question-circle me-2"></i>常见问题解决</h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                仍然显示"暂无信息"怎么办？
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <ol>
                                    <li>确认MySQL驱动文件在两个位置都存在</li>
                                    <li>检查数据库是否有数据：<code>SELECT COUNT(*) FROM tourists;</code></li>
                                    <li>查看服务器控制台是否有错误信息</li>
                                    <li>重启服务器后再次测试</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                404错误如何解决？
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>404错误通常是因为：</p>
                                <ul>
                                    <li>Servlet映射配置问题</li>
                                    <li>应用部署路径不正确</li>
                                    <li>web.xml配置有误</li>
                                </ul>
                                <p>解决方法：重启服务器，确保应用正确部署</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
