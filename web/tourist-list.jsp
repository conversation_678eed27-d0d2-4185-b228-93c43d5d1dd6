<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游客管理 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navbar-brand { font-weight: bold; }
        .table-container { background: white; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .btn-group .btn { margin-right: 5px; }
        .search-container { background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .status-badge { font-size: 0.8em; }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="${pageContext.request.contextPath}/">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-users text-primary me-2"></i>游客管理</h2>
            <a href="${pageContext.request.contextPath}/tourist?action=add" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>添加游客
            </a>
        </div>

        <!-- 消息提示 -->
        <c:if test="${not empty sessionScope.message}">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>${sessionScope.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <c:remove var="message" scope="session"/>
        </c:if>

        <c:if test="${not empty sessionScope.error}">
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>${sessionScope.error}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <c:remove var="error" scope="session"/>
        </c:if>

        <!-- 搜索区域 -->
        <div class="search-container">
            <form method="get" action="${pageContext.request.contextPath}/tourist">
                <input type="hidden" name="action" value="search">
                <div class="row g-3">
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="name" placeholder="游客姓名" value="${param.name}">
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="phone" placeholder="联系电话" value="${param.phone}">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="customerType">
                            <option value="">选择客户类型</option>
                            <option value="VIP客户" ${param.customerType == 'VIP客户' ? 'selected' : ''}>VIP客户</option>
                            <option value="常客" ${param.customerType == '常客' ? 'selected' : ''}>常客</option>
                            <option value="新客户" ${param.customerType == '新客户' ? 'selected' : ''}>新客户</option>
                            <option value="商务客户" ${param.customerType == '商务客户' ? 'selected' : ''}>商务客户</option>
                            <option value="家庭客户" ${param.customerType == '家庭客户' ? 'selected' : ''}>家庭客户</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="btn-group w-100">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-1"></i>搜索
                            </button>
                            <a href="${pageContext.request.contextPath}/tourist" class="btn btn-outline-secondary">
                                <i class="fas fa-refresh me-1"></i>重置
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 游客列表 -->
        <div class="table-container p-4">
            <c:choose>
                <c:when test="${not empty tourists}">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-primary">
                                <tr>
                                    <th>ID</th>
                                    <th>姓名</th>
                                    <th>身份证号</th>
                                    <th>联系电话</th>
                                    <th>邮箱</th>
                                    <th>客户类型</th>
                                    <th>地址</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach var="tourist" items="${tourists}">
                                    <tr>
                                        <td>${tourist.id}</td>
                                        <td><strong>${tourist.name}</strong></td>
                                        <td><code>${tourist.idCard}</code></td>
                                        <td>
                                            <i class="fas fa-phone text-success me-1"></i>${tourist.phone}
                                        </td>
                                        <td>
                                            <c:if test="${not empty tourist.email}">
                                                <i class="fas fa-envelope text-info me-1"></i>${tourist.email}
                                            </c:if>
                                        </td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${tourist.customerType == 'VIP客户'}">
                                                    <span class="badge bg-warning status-badge">${tourist.customerType}</span>
                                                </c:when>
                                                <c:when test="${tourist.customerType == '常客'}">
                                                    <span class="badge bg-success status-badge">${tourist.customerType}</span>
                                                </c:when>
                                                <c:when test="${tourist.customerType == '新客户'}">
                                                    <span class="badge bg-primary status-badge">${tourist.customerType}</span>
                                                </c:when>
                                                <c:when test="${tourist.customerType == '商务客户'}">
                                                    <span class="badge bg-info status-badge">${tourist.customerType}</span>
                                                </c:when>
                                                <c:when test="${tourist.customerType == '家庭客户'}">
                                                    <span class="badge bg-secondary status-badge">${tourist.customerType}</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span class="badge bg-light text-dark status-badge">${tourist.customerType}</span>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td>
                                            <c:if test="${not empty tourist.address}">
                                                <i class="fas fa-map-marker-alt text-danger me-1"></i>${tourist.address}
                                            </c:if>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="${pageContext.request.contextPath}/tourist?action=edit&id=${tourist.id}" 
                                                   class="btn btn-outline-primary" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="${pageContext.request.contextPath}/tourist?action=delete&id=${tourist.id}" 
                                                   class="btn btn-outline-danger" title="删除"
                                                   onclick="return confirm('确定要删除游客 ${tourist.name} 吗？')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 统计信息 -->
                    <div class="mt-3 text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        共找到 <strong>${tourists.size()}</strong> 位游客
                    </div>
                </c:when>
                <c:otherwise>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无游客信息</h5>
                        <p class="text-muted">点击上方"添加游客"按钮添加第一位游客</p>
                        <a href="${pageContext.request.contextPath}/tourist?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>添加游客
                        </a>
                    </div>
                </c:otherwise>
            </c:choose>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
