<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.tourism.util.DatabaseUtil" %>
<%@ page import="java.sql.*" %>
<%
    String contextPath = request.getContextPath();
    
    // 测试数据库连接
    boolean connectionSuccess = false;
    String connectionMessage = "";
    String databaseInfo = "";
    String tableInfo = "";
    
    try {
        databaseInfo = DatabaseUtil.getDatabaseInfo();
        connectionSuccess = DatabaseUtil.testConnection();
        
        if (connectionSuccess) {
            connectionMessage = "数据库连接成功！";
            
            // 检查表是否存在
            Connection conn = DatabaseUtil.getConnection();
            DatabaseMetaData metaData = conn.getMetaData();
            
            String[] tableNames = {"tourists", "hotels", "travel_agencies", "guides", "apartments", "complaints", "enterprises"};
            StringBuilder tableStatus = new StringBuilder();
            
            for (String tableName : tableNames) {
                ResultSet tables = metaData.getTables(null, null, tableName, null);
                if (tables.next()) {
                    tableStatus.append("✅ ").append(tableName).append(" 表存在\n");
                } else {
                    tableStatus.append("❌ ").append(tableName).append(" 表不存在\n");
                }
                tables.close();
            }
            
            tableInfo = tableStatus.toString();
            conn.close();
        } else {
            connectionMessage = "数据库连接失败！请检查配置。";
        }
    } catch (Exception e) {
        connectionSuccess = false;
        connectionMessage = "数据库连接错误: " + e.getMessage();
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库连接测试 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/">返回首页</a>
                <a class="nav-link" href="<%=contextPath%>/test-simple.jsp">功能测试</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-database me-2"></i>数据库连接测试</h2>
                
                <!-- 连接状态 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">连接状态</h5>
                    </div>
                    <div class="card-body">
                        <% if (connectionSuccess) { %>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i><%=connectionMessage%>
                            </div>
                        <% } else { %>
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle me-2"></i><%=connectionMessage%>
                            </div>
                        <% } %>
                    </div>
                </div>

                <!-- 数据库配置信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">数据库配置信息</h5>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded"><%=databaseInfo%></pre>
                    </div>
                </div>

                <!-- 表结构检查 -->
                <% if (connectionSuccess && !tableInfo.isEmpty()) { %>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">数据表检查</h5>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded"><%=tableInfo%></pre>
                    </div>
                </div>
                <% } %>

                <!-- 配置说明 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">配置说明</h5>
                    </div>
                    <div class="card-body">
                        <h6>1. 数据库配置文件</h6>
                        <p>配置文件位置: <code>src/database.properties</code></p>
                        <p>如果连接失败，请检查以下配置：</p>
                        <ul>
                            <li><strong>数据库服务</strong>: 确保MySQL服务已启动</li>
                            <li><strong>数据库名称</strong>: 确保 <code>tourism_system</code> 数据库已创建</li>
                            <li><strong>用户名密码</strong>: 检查MySQL用户名和密码</li>
                            <li><strong>端口号</strong>: 默认3306，如有修改请更新配置</li>
                        </ul>
                        
                        <h6 class="mt-4">2. 常见配置</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>默认配置:</h6>
                                <pre class="bg-light p-2 small">
URL: ******************************************
用户名: root
密码: 123456</pre>
                            </div>
                            <div class="col-md-6">
                                <h6>备用配置:</h6>
                                <pre class="bg-light p-2 small">
URL: ******************************************
用户名: root
密码: root</pre>
                            </div>
                        </div>
                        
                        <h6 class="mt-4">3. 数据库初始化</h6>
                        <p>如果数据表不存在，请运行数据库初始化脚本：</p>
                        <pre class="bg-dark text-light p-2"><code>mysql -u root -p tourism_system < database/init.sql</code></pre>
                        
                        <h6 class="mt-4">4. 创建数据库</h6>
                        <p>如果数据库不存在，请先创建：</p>
                        <pre class="bg-dark text-light p-2"><code>CREATE DATABASE tourism_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;</code></pre>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">快速操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/database-test.jsp" class="btn btn-primary w-100">
                                    <i class="fas fa-refresh me-2"></i>重新测试
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/test-simple.jsp" class="btn btn-success w-100">
                                    <i class="fas fa-play me-2"></i>功能测试
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/tourist" class="btn btn-info w-100">
                                    <i class="fas fa-users me-2"></i>游客管理
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="<%=contextPath%>/" class="btn btn-secondary w-100">
                                    <i class="fas fa-home me-2"></i>返回首页
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
