<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%
    String contextPath = request.getContextPath();
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加公寓 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="<%=contextPath%>/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<%=contextPath%>/apartment">返回公寓列表</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-building me-2"></i>添加公寓</h4>
                    </div>
                    <div class="card-body">
                        <form method="post" action="<%=contextPath%>/apartment" class="needs-validation" novalidate>
                            <input type="hidden" name="action" value="add">
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="name" class="form-label">公寓名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                    <div class="invalid-feedback">请输入公寓名称</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="roomType" class="form-label">房间类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="roomType" name="roomType" required>
                                        <option value="">选择房间类型</option>
                                        <option value="单人间">单人间</option>
                                        <option value="双人间">双人间</option>
                                        <option value="套房">套房</option>
                                        <option value="家庭房">家庭房</option>
                                        <option value="豪华套房">豪华套房</option>
                                    </select>
                                    <div class="invalid-feedback">请选择房间类型</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="address" class="form-label">地址 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="address" name="address" required>
                                    <div class="invalid-feedback">请输入地址</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="phone" class="form-label">电话 <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                    <div class="invalid-feedback">请输入电话</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="roomCount" class="form-label">房间数量 <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="roomCount" name="roomCount" 
                                           min="1" max="999" required>
                                    <div class="invalid-feedback">请输入房间数量</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="pricePerNight" class="form-label">每晚价格(元) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="pricePerNight" name="pricePerNight" 
                                           step="0.01" min="0" required>
                                    <div class="invalid-feedback">请输入每晚价格</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="email" class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="manager" class="form-label">负责人</label>
                                    <input type="text" class="form-control" id="manager" name="manager">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">预设状态</label>
                                    <div class="form-control-plaintext">
                                        <span class="badge bg-success">可预订</span>
                                        <small class="text-muted ms-2">新添加的公寓默认为可预订状态</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="facilities" class="form-label">设施服务</label>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="facility1" value="WiFi" onchange="updateFacilities()">
                                            <label class="form-check-label" for="facility1">WiFi</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="facility2" value="空调" onchange="updateFacilities()">
                                            <label class="form-check-label" for="facility2">空调</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="facility3" value="电视" onchange="updateFacilities()">
                                            <label class="form-check-label" for="facility3">电视</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="facility4" value="冰箱" onchange="updateFacilities()">
                                            <label class="form-check-label" for="facility4">冰箱</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="facility5" value="洗衣机" onchange="updateFacilities()">
                                            <label class="form-check-label" for="facility5">洗衣机</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="facility6" value="厨房" onchange="updateFacilities()">
                                            <label class="form-check-label" for="facility6">厨房</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="facility7" value="阳台" onchange="updateFacilities()">
                                            <label class="form-check-label" for="facility7">阳台</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="facility8" value="停车位" onchange="updateFacilities()">
                                            <label class="form-check-label" for="facility8">停车位</label>
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" id="facilities" name="facilities">
                                <small class="form-text text-muted">请选择公寓提供的设施和服务</small>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">公寓描述</label>
                                <textarea class="form-control" id="description" name="description" rows="4" 
                                          placeholder="公寓的详细介绍、周边环境、交通便利性等"></textarea>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="<%=contextPath%>/apartment" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回
                                </a>
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-save me-2"></i>保存
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 更新设施服务
        function updateFacilities() {
            var checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            var facilities = [];
            checkboxes.forEach(function(checkbox) {
                facilities.push(checkbox.value);
            });
            document.getElementById('facilities').value = facilities.join(',');
        }
        
        // 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
                
                // 默认选中基础设施
                document.getElementById('facility1').checked = true; // WiFi
                document.getElementById('facility2').checked = true; // 空调
                updateFacilities();
            }, false);
        })();
    </script>
</body>
</html>
