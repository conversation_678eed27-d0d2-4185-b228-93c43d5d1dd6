# 旅游行业管理系统 - 部署指南

## 快速部署步骤

### 1. 环境准备

#### 必需软件
- **JDK 8+**: 确保安装了Java开发工具包
- **Apache Tomcat 9.0+**: Web应用服务器
- **MySQL 8.0+**: 数据库服务器
- **IDE**: IntelliJ IDEA 或 Eclipse（开发时使用）

#### 验证环境
```bash
# 检查Java版本
java -version

# 检查MySQL是否运行
mysql --version
```

### 2. 数据库配置

#### 2.1 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE tourism_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 2.2 执行初始化脚本
```bash
# 执行数据库初始化脚本
mysql -u root -p tourism_system < database/init.sql
```

#### 2.3 创建数据库用户（可选）
```sql
-- 创建专用用户
CREATE USER 'tourism_user'@'localhost' IDENTIFIED BY 'tourism_password';
GRANT ALL PRIVILEGES ON tourism_system.* TO 'tourism_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 项目配置

#### 3.1 修改数据库连接配置
编辑文件：`src/com/tourism/util/DatabaseUtil.java`

```java
// 修改以下配置
private static final String URL = "*************************************************************************************************";
private static final String USERNAME = "root";  // 或 tourism_user
private static final String PASSWORD = "123456"; // 或 tourism_password
```

#### 3.2 添加MySQL JDBC驱动
下载MySQL Connector/J驱动，将jar文件放入：
- `web/WEB-INF/lib/` 目录（推荐）
- 或者添加到Tomcat的 `lib` 目录

### 4. 编译和打包

#### 4.1 使用IDE编译
1. 在IntelliJ IDEA中打开项目
2. 确保项目SDK设置正确
3. Build → Build Project

#### 4.2 手动编译（可选）
```bash
# 编译Java源文件
javac -cp "web/WEB-INF/lib/*" -d web/WEB-INF/classes src/com/tourism/**/*.java
```

### 5. 部署到Tomcat

#### 5.1 方法一：直接部署
1. 将整个 `web` 目录复制到 Tomcat 的 `webapps` 目录
2. 重命名为 `tourism` 或保持 `webtest`

#### 5.2 方法二：WAR包部署
1. 将 `web` 目录打包为 WAR 文件
2. 将 WAR 文件放入 Tomcat 的 `webapps` 目录

```bash
# 创建WAR包
cd web
jar -cvf ../tourism.war *
```

### 6. 启动服务

#### 6.1 启动MySQL
```bash
# Linux/Mac
sudo systemctl start mysql

# Windows
net start mysql
```

#### 6.2 启动Tomcat
```bash
# Linux/Mac
$CATALINA_HOME/bin/startup.sh

# Windows
%CATALINA_HOME%\bin\startup.bat
```

### 7. 访问系统

打开浏览器访问：
- `http://localhost:8080/webtest/` （如果使用原目录名）
- `http://localhost:8080/tourism/` （如果重命名为tourism）

### 8. 验证部署

#### 8.1 检查首页
- 访问系统首页，确保页面正常显示
- 检查导航菜单是否正常

#### 8.2 测试功能
- 点击"游客管理"进入游客管理页面
- 尝试添加一个测试游客
- 验证数据是否正确保存到数据库

### 9. 常见问题解决

#### 9.1 数据库连接失败
- 检查MySQL服务是否启动
- 验证数据库连接参数是否正确
- 确认MySQL JDBC驱动是否正确添加

#### 9.2 页面显示乱码
- 确认数据库字符集为utf8mb4
- 检查Tomcat的字符编码配置
- 验证JSP页面的编码设置

#### 9.3 404错误
- 检查Tomcat是否正常启动
- 验证项目是否正确部署到webapps目录
- 确认访问URL是否正确

#### 9.4 500错误
- 查看Tomcat日志文件
- 检查Java类是否正确编译
- 验证数据库连接是否正常

### 10. 日志查看

#### Tomcat日志位置
- `$CATALINA_HOME/logs/catalina.out`
- `$CATALINA_HOME/logs/localhost.log`

#### 查看实时日志
```bash
# Linux/Mac
tail -f $CATALINA_HOME/logs/catalina.out

# Windows
type %CATALINA_HOME%\logs\catalina.out
```

### 11. 性能优化建议

#### 11.1 数据库优化
- 为常用查询字段添加索引
- 定期清理过期数据
- 配置数据库连接池

#### 11.2 应用优化
- 启用Tomcat的GZIP压缩
- 配置静态资源缓存
- 优化JSP页面加载

### 12. 安全配置

#### 12.1 数据库安全
- 使用专用数据库用户
- 限制数据库用户权限
- 定期更新数据库密码

#### 12.2 应用安全
- 配置HTTPS（生产环境）
- 设置合适的会话超时时间
- 添加输入验证和SQL注入防护

## 技术支持

如果在部署过程中遇到问题，请：
1. 检查系统日志
2. 验证环境配置
3. 参考常见问题解决方案
4. 联系技术支持团队
